{"name": "my-app-next", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo", "dev:fast": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "clean-dev": "rmdir /s /q .next 2>nul & rmdir /s /q node_modules\\.cache 2>nul & next dev --turbo", "clean": "rmdir /s /q .next 2>nul & rmdir /s /q node_modules\\.cache 2>nul", "analyze": "next build", "type-check": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@tabler/icons-react": "^3.34.0", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.15.0", "lucide-react": "^0.513.0", "next": "^15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "recharts": "^2.15.3", "tailwind-merge": "^3.2.0", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "22.15.16", "@types/react": "19.1.3", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4"}}