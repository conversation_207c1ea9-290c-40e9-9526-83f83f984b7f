# Generated by Django 3.2.25 on 2025-07-08 23:53

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('jobs', '0013_auto_20250704_1155'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='jobapplication',
            options={'ordering': ['-applied_at']},
        ),
        migrations.AddField(
            model_name='jobapplication',
            name='admin_notes',
            field=models.TextField(blank=True, help_text='Internal admin notes', null=True),
        ),
        migrations.AddField(
            model_name='jobapplication',
            name='deleted_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='jobapplication',
            name='is_deleted',
            field=models.BooleanField(default=False, help_text='Soft delete flag'),
        ),
        migrations.Add<PERSON>ield(
            model_name='jobapplication',
            name='last_modified_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='modified_applications', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='jobapplication',
            name='status_history',
            field=models.JSONField(blank=True, default=list, help_text='Status change history'),
        ),
        migrations.AlterField(
            model_name='jobapplication',
            name='applied_data_snapshot',
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AddIndex(
            model_name='jobapplication',
            index=models.Index(fields=['status', 'applied_at'], name='jobs_jobapp_status_6642d6_idx'),
        ),
        migrations.AddIndex(
            model_name='jobapplication',
            index=models.Index(fields=['job', 'status'], name='jobs_jobapp_job_id_08192b_idx'),
        ),
        migrations.AddIndex(
            model_name='jobapplication',
            index=models.Index(fields=['applicant', 'applied_at'], name='jobs_jobapp_applica_0be4e6_idx'),
        ),
    ]
