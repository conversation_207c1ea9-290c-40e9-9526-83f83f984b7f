# Generated by Django 5.1.5 on 2025-09-21 13:48

import accounts.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0016_alter_resume_file_alter_studentprofile_resume_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='resume',
            name='file',
            field=models.FileField(help_text='Resume file', upload_to=accounts.models.student_resume_upload_path, validators=[accounts.models.validate_resume_file]),
        ),
        migrations.AlterField(
            model_name='studentprofile',
            name='resume',
            field=models.FileField(blank=True, null=True, upload_to=accounts.models.student_resume_upload_path, validators=[accounts.models.validate_resume_file]),
        ),
        migrations.AlterField(
            model_name='studentprofile',
            name='semester1_marksheet',
            field=models.FileField(blank=True, null=True, upload_to=accounts.models.student_semester1_marksheet_upload_path, validators=[accounts.models.validate_certificate_file]),
        ),
        migrations.AlterField(
            model_name='studentprofile',
            name='semester2_marksheet',
            field=models.FileField(blank=True, null=True, upload_to=accounts.models.student_semester2_marksheet_upload_path, validators=[accounts.models.validate_certificate_file]),
        ),
        migrations.AlterField(
            model_name='studentprofile',
            name='semester3_marksheet',
            field=models.FileField(blank=True, null=True, upload_to=accounts.models.student_semester3_marksheet_upload_path, validators=[accounts.models.validate_certificate_file]),
        ),
        migrations.AlterField(
            model_name='studentprofile',
            name='semester4_marksheet',
            field=models.FileField(blank=True, null=True, upload_to=accounts.models.student_semester4_marksheet_upload_path, validators=[accounts.models.validate_certificate_file]),
        ),
        migrations.AlterField(
            model_name='studentprofile',
            name='semester5_marksheet',
            field=models.FileField(blank=True, null=True, upload_to=accounts.models.student_semester5_marksheet_upload_path, validators=[accounts.models.validate_certificate_file]),
        ),
        migrations.AlterField(
            model_name='studentprofile',
            name='semester6_marksheet',
            field=models.FileField(blank=True, null=True, upload_to=accounts.models.student_semester6_marksheet_upload_path, validators=[accounts.models.validate_certificate_file]),
        ),
        migrations.AlterField(
            model_name='studentprofile',
            name='semester7_marksheet',
            field=models.FileField(blank=True, null=True, upload_to=accounts.models.student_semester7_marksheet_upload_path, validators=[accounts.models.validate_certificate_file]),
        ),
        migrations.AlterField(
            model_name='studentprofile',
            name='semester8_marksheet',
            field=models.FileField(blank=True, null=True, upload_to=accounts.models.student_semester8_marksheet_upload_path, validators=[accounts.models.validate_certificate_file]),
        ),
        migrations.AlterField(
            model_name='studentprofile',
            name='tenth_certificate',
            field=models.FileField(blank=True, null=True, upload_to=accounts.models.student_tenth_certificate_upload_path, validators=[accounts.models.validate_certificate_file]),
        ),
        migrations.AlterField(
            model_name='studentprofile',
            name='twelfth_certificate',
            field=models.FileField(blank=True, null=True, upload_to=accounts.models.student_twelfth_certificate_upload_path, validators=[accounts.models.validate_certificate_file]),
        ),
    ]
