{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_adbadb87._.js", "server/edge/chunks/[root-of-the-server]__e07e566a._.js", "server/edge/chunks/edge-wrapper_9cb4b359.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "4L1N1ApV6JxRkywf2zSy/shy6TmjdExKn7foFPP8JTY=", "__NEXT_PREVIEW_MODE_ID": "f98c4ed7cea01d7d6b3ca4e4dc52064a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "fdf1b9325b25de0df10883a584498cab4390f0e8c88dcfe0cc2e3ac53211b558", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "89ce901c0b579cac677bd1819f65b80d09d3925318c43f5feef00226582f892e"}}}, "sortedMiddleware": ["/"], "functions": {}}