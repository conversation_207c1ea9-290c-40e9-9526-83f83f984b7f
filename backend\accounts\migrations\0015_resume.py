# Generated by Django 5.1.5 on 2025-09-21 07:12

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0014_studentprofile_allowed_companies_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Resume',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Display name for the resume', max_length=255)),
                ('file', models.FileField(help_text='Resume file', upload_to='resumes/')),
                ('file_size', models.PositiveIntegerField(blank=True, help_text='File size in bytes', null=True)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_primary', models.<PERSON><PERSON><PERSON><PERSON>ield(default=False, help_text='Whether this is the primary resume')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='resumes', to='accounts.studentprofile')),
            ],
            options={
                'ordering': ['-is_primary', '-uploaded_at'],
            },
        ),
    ]
