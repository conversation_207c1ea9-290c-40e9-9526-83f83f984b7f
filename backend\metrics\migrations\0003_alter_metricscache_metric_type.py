# Generated by Django 5.1.5 on 2025-07-03 06:54

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('metrics', '0002_enhance_metrics_system'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='metricscache',
            name='metric_type',
            field=models.CharField(choices=[('dashboard_stats', 'Dashboard Statistics'), ('company_stats', 'Company Statistics'), ('student_stats', 'Student Statistics'), ('enhanced_student_stats', 'Enhanced Student Statistics'), ('student_department_breakdown', 'Student Department Breakdown'), ('student_year_analysis', 'Student Year Analysis'), ('job_stats', 'Job Statistics'), ('application_stats', 'Application Statistics'), ('placement_stats', 'Placement Statistics'), ('department_stats', 'Department Statistics'), ('recruitment_stats', 'Recruitment Statistics'), ('performance_stats', 'Performance Statistics'), ('trend_stats', 'Trend Statistics')], max_length=50),
        ),
    ]
