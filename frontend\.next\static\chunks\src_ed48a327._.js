(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/api/errorHandler.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ERROR_PATTERNS": (()=>ERROR_PATTERNS),
    "default": (()=>__TURBOPACK__default__export__),
    "detectAndHandleError": (()=>detectAndHandleError),
    "setupErrorInterceptor": (()=>setupErrorInterceptor),
    "useErrorHandler": (()=>useErrorHandler)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$NotificationContext$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/NotificationContext.jsx [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
;
const ERROR_PATTERNS = {
    AUTHENTICATION: {
        codes: [
            401
        ],
        keywords: [
            'unauthorized',
            'authentication',
            'token',
            'login'
        ],
        handler: 'showAuthError'
    },
    SESSION_EXPIRED: {
        codes: [
            401
        ],
        keywords: [
            'expired',
            'invalid token',
            'token expired'
        ],
        handler: 'showSessionExpiredModal'
    },
    PERMISSION_DENIED: {
        codes: [
            403
        ],
        keywords: [
            'permission',
            'forbidden',
            'access denied'
        ],
        handler: 'showAuthError'
    },
    VALIDATION: {
        codes: [
            400,
            422
        ],
        keywords: [
            'validation',
            'invalid',
            'required'
        ],
        handler: 'showValidationError'
    },
    RESUME_REQUIRED: {
        fields: [
            'resume'
        ],
        keywords: [
            'resume',
            'must be uploaded',
            'present in the student profile'
        ],
        handler: 'showMissingResumeModal'
    },
    PROFILE_INCOMPLETE: {
        keywords: [
            'profile incomplete',
            'missing profile',
            'update profile'
        ],
        handler: 'showProfileIncompleteModal'
    },
    FILE_UPLOAD: {
        keywords: [
            'file',
            'upload',
            'size',
            'format',
            'extension'
        ],
        handler: 'showFileUploadError'
    },
    NETWORK_ERROR: {
        codes: [
            'NETWORK_ERROR',
            'ECONNREFUSED',
            'ERR_NETWORK'
        ],
        keywords: [
            'network',
            'connection',
            'timeout'
        ],
        handler: 'showNetworkError'
    },
    MAINTENANCE: {
        codes: [
            503,
            502
        ],
        keywords: [
            'maintenance',
            'service unavailable',
            'temporarily unavailable'
        ],
        handler: 'showMaintenanceModal'
    }
};
const detectAndHandleError = (error, context = '', notificationHandlers)=>{
    const errorData = error?.response?.data || {};
    const errorMessage = (errorData.detail || errorData.message || error.message || '').toLowerCase();
    const statusCode = error?.response?.status;
    // Check for specific error patterns
    for (const [pattern, config] of Object.entries(ERROR_PATTERNS)){
        // Check status codes
        if (config.codes && config.codes.includes(statusCode)) {
            // Additional keyword check for more precision
            if (config.keywords && !config.keywords.some((keyword)=>errorMessage.includes(keyword))) {
                continue;
            }
            return handleSpecificError(pattern, error, context, notificationHandlers);
        }
        // Check for field-specific errors (like resume)
        if (config.fields && config.fields.some((field)=>errorData[field])) {
            return handleSpecificError(pattern, error, context, notificationHandlers);
        }
        // Check keywords in error message
        if (config.keywords && config.keywords.some((keyword)=>errorMessage.includes(keyword))) {
            return handleSpecificError(pattern, error, context, notificationHandlers);
        }
    }
    // Fallback to generic error handling
    return handleGenericError(error, context, notificationHandlers);
};
const handleSpecificError = (pattern, error, context, notificationHandlers)=>{
    const config = ERROR_PATTERNS[pattern];
    const handlerName = config.handler;
    if (notificationHandlers[handlerName]) {
        switch(handlerName){
            case 'showMissingResumeModal':
                notificationHandlers.showMissingResumeModal();
                break;
            case 'showSessionExpiredModal':
                notificationHandlers.showSessionExpiredModal();
                break;
            case 'showMaintenanceModal':
                notificationHandlers.showMaintenanceModal();
                break;
            case 'showValidationError':
                const errorData = error?.response?.data || {};
                notificationHandlers.showValidationError(`Validation Error ${context ? `in ${context}` : ''}`, errorData);
                break;
            case 'showAuthError':
                const message = error?.response?.data?.detail || error?.response?.data?.message || `Authentication failed${context ? ` while ${context}` : ''}`;
                notificationHandlers.showAuthError(message);
                break;
            case 'showFileUploadError':
                notificationHandlers.showFileUploadError();
                break;
            case 'showNetworkError':
                notificationHandlers.showNetworkError(error);
                break;
            case 'showProfileIncompleteModal':
                notificationHandlers.showProfileIncompleteModal();
                break;
            default:
                return handleGenericError(error, context, notificationHandlers);
        }
        return true; // Error was handled
    }
    return false; // Error not handled
};
const handleGenericError = (error, context, notificationHandlers)=>{
    if (notificationHandlers.handleApiError) {
        notificationHandlers.handleApiError(error, context);
        return true;
    }
    // Ultimate fallback
    console.error('Unhandled error:', error);
    return false;
};
const useErrorHandler = ()=>{
    _s();
    const notificationHandlers = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$NotificationContext$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useNotification"])();
    const handleError = (error, context = '')=>{
        return detectAndHandleError(error, context, notificationHandlers);
    };
    return {
        handleError
    };
};
_s(useErrorHandler, "2+3vdp+Gj38tyC21Sdt5jR3pRwQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$NotificationContext$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useNotification"]
    ];
});
const setupErrorInterceptor = (axiosInstance, notificationHandlers)=>{
    axiosInstance.interceptors.response.use({
        "setupErrorInterceptor.use": (response)=>response
    }["setupErrorInterceptor.use"], {
        "setupErrorInterceptor.use": (error)=>{
            // Automatically handle common errors
            detectAndHandleError(error, 'API request', notificationHandlers);
            return Promise.reject(error);
        }
    }["setupErrorInterceptor.use"]);
};
const __TURBOPACK__default__export__ = {
    detectAndHandleError,
    useErrorHandler,
    setupErrorInterceptor,
    ERROR_PATTERNS
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/api/client.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$errorHandler$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/api/errorHandler.js [app-client] (ecmascript)");
;
;
const client = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: ("TURBOPACK compile-time value", "http://localhost:8000") || 'http://127.0.0.1:8000',
    headers: {
        'Content-Type': 'application/json'
    }
});
// Add a request interceptor to include the auth token
client.interceptors.request.use((config)=>{
    // Get the token from localStorage
    const token = localStorage.getItem('access_token');
    // If token exists, add it to the Authorization header
    if (token) {
        config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
}, (error)=>{
    return Promise.reject(error);
});
// Add a response interceptor to handle 401 errors (token expired)
client.interceptors.response.use((response)=>response, async (error)=>{
    const originalRequest = error.config;
    // If error is 401 and we haven't tried to refresh the token yet
    if (error.response?.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true;
        try {
            // Get refresh token
            const refreshToken = localStorage.getItem('refresh_token');
            if (refreshToken) {
                // Try to get a new token
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post('http://127.0.0.1:8000/api/auth/token/refresh/', {
                    refresh: refreshToken
                });
                // Store the new tokens
                localStorage.setItem('access_token', response.data.access);
                // Update the Authorization header
                originalRequest.headers['Authorization'] = `Bearer ${response.data.access}`;
                // Retry the original request
                return client(originalRequest);
            }
        } catch (refreshError) {
            console.error('Error refreshing token:', refreshError);
            // If token refresh fails, redirect to login
            if ("TURBOPACK compile-time truthy", 1) {
                // Clear tokens
                localStorage.removeItem('access_token');
                localStorage.removeItem('refresh_token');
                // Redirect to login page
                window.location.href = '/login';
            }
        }
    }
    return Promise.reject(error);
});
const __TURBOPACK__default__export__ = client;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/api/auth.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "getAuthToken": (()=>getAuthToken),
    "getRefreshToken": (()=>getRefreshToken),
    "login": (()=>login),
    "removeAuthToken": (()=>removeAuthToken),
    "setAuthToken": (()=>setAuthToken),
    "setRefreshToken": (()=>setRefreshToken),
    "signup": (()=>signup),
    "uploadResume": (()=>uploadResume)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/api/client.js [app-client] (ecmascript)");
;
function signup(data) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post('/api/auth/register/student/', data);
}
function login(data) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post('/api/auth/login/', data);
}
function uploadResume(file, accessToken) {
    const formData = new FormData();
    formData.append('resume', file);
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].patch('/api/auth/profile/', formData, {
        headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'multipart/form-data'
        }
    });
}
const getAuthToken = ()=>{
    if ("TURBOPACK compile-time truthy", 1) {
        return localStorage.getItem('access_token');
    }
    "TURBOPACK unreachable";
};
const setAuthToken = (token)=>{
    if ("TURBOPACK compile-time truthy", 1) {
        localStorage.setItem('access_token', token);
    }
};
const removeAuthToken = ()=>{
    if ("TURBOPACK compile-time truthy", 1) {
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
    }
};
const getRefreshToken = ()=>{
    if ("TURBOPACK compile-time truthy", 1) {
        return localStorage.getItem('refresh_token');
    }
    "TURBOPACK unreachable";
};
const setRefreshToken = (token)=>{
    if ("TURBOPACK compile-time truthy", 1) {
        localStorage.setItem('refresh_token', token);
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/api/students.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "studentsAPI": (()=>studentsAPI)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/api/auth.js [app-client] (ecmascript)");
;
;
// Set the base URL for all API requests
const API_BASE_URL = 'http://localhost:8000';
const api = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: API_BASE_URL,
    headers: {
        'Content-Type': 'application/json'
    }
});
// Add request interceptor to include auth token
api.interceptors.request.use((config)=>{
    const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
}, (error)=>{
    return Promise.reject(error);
});
// Add response interceptor for error handling
api.interceptors.response.use((response)=>response, (error)=>{
    if (error.response?.status === 401) {
        // Token expired or invalid
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        window.location.href = '/login';
    }
    return Promise.reject(error);
});
const studentsAPI = {
    // Get all students
    getStudents: async (params = {})=>{
        const response = await api.get('/api/accounts/students/', {
            params
        });
        return response.data;
    },
    // Get students with statistics
    getStudentsWithStats: async (params = {})=>{
        try {
            // First try to get students with built-in statistics
            const response = await api.get('/api/accounts/students/stats/', {
                params
            });
            return response.data;
        } catch (error) {
            // Fallback to regular students endpoint
            console.log('Stats endpoint not available, using regular endpoint');
            const response = await api.get('/api/accounts/students/', {
                params
            });
            // Calculate basic statistics from the response
            const students = response.data.data || response.data;
            if (Array.isArray(students)) {
                const stats = calculateStudentStats(students, params);
                return {
                    ...response.data,
                    statistics: stats
                };
            }
            return response.data;
        }
    },
    // Get single student
    getStudent: async (id)=>{
        const response = await api.get(`/api/accounts/students/${id}/`);
        return response.data;
    },
    // Update student
    updateStudent: async (id, data)=>{
        console.log('updateStudent called with:', {
            id,
            data
        });
        // Check authentication
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        console.log('Auth token available:', !!token);
        if (token) {
            console.log('Token preview:', token.substring(0, 20) + '...');
        }
        if (!token) {
            throw new Error('Authentication required to update student');
        }
        // Clean data to ensure proper format
        const cleanedData = {
            ...data
        };
        // Ensure numeric fields are properly formatted
        [
            'joining_year',
            'passout_year'
        ].forEach((field)=>{
            if (cleanedData[field] !== null && cleanedData[field] !== undefined) {
                const num = parseInt(cleanedData[field]);
                cleanedData[field] = isNaN(num) ? null : num;
            }
        });
        // Ensure string fields are properly formatted
        const stringFields = [
            'first_name',
            'last_name',
            'student_id',
            'contact_email',
            'phone',
            'branch',
            'gpa',
            'date_of_birth',
            'address',
            'city',
            'district',
            'state',
            'pincode',
            'country',
            'parent_contact',
            'education',
            'skills',
            'tenth_cgpa',
            'tenth_percentage',
            'tenth_board',
            'tenth_school',
            'tenth_year_of_passing',
            'tenth_location',
            'tenth_specialization',
            'twelfth_cgpa',
            'twelfth_percentage',
            'twelfth_board',
            'twelfth_school',
            'twelfth_year_of_passing',
            'twelfth_location',
            'twelfth_specialization'
        ];
        stringFields.forEach((field)=>{
            if (cleanedData[field] !== null && cleanedData[field] !== undefined) {
                cleanedData[field] = String(cleanedData[field]).trim();
            }
        });
        // Remove undefined values
        Object.keys(cleanedData).forEach((key)=>{
            if (cleanedData[key] === undefined) {
                delete cleanedData[key];
            }
        });
        console.log('Cleaned data being sent:', cleanedData);
        // Try the ViewSet endpoint first (more RESTful)
        try {
            console.log('Trying ViewSet endpoint:', `/api/accounts/profiles/${id}/`);
            const response = await api.patch(`/api/accounts/profiles/${id}/`, cleanedData);
            console.log('ViewSet endpoint success:', response.data);
            return response.data;
        } catch (error) {
            console.error('ViewSet endpoint failed:', {
                status: error.response?.status,
                statusText: error.response?.statusText,
                data: error.response?.data,
                headers: error.response?.headers,
                config: error.config
            });
            // If ViewSet fails, try the fallback endpoint
            try {
                console.log('Trying fallback endpoint:', `/api/accounts/students/${id}/update/`);
                const response = await api.patch(`/api/accounts/students/${id}/update/`, cleanedData);
                console.log('Fallback endpoint success:', response.data);
                return response.data;
            } catch (updateError) {
                console.error('Failed to update student via both endpoints:', {
                    viewSetError: {
                        status: error.response?.status,
                        data: error.response?.data
                    },
                    updateViewError: {
                        status: updateError.response?.status,
                        data: updateError.response?.data
                    }
                });
                // Throw the more specific error
                const primaryError = updateError.response?.status === 400 ? updateError : error;
                throw primaryError;
            }
        }
    },
    // Get current user profile
    getProfile: async ()=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        return api.get('/api/auth/profile/', {
            headers: {
                Authorization: `Bearer ${token}`
            }
        }).then((response)=>response.data);
    },
    // Update profile information
    updateProfile: async (data)=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        return api.patch('/api/auth/profile/', data, {
            headers: {
                Authorization: `Bearer ${token}`
            }
        }).then((response)=>response.data);
    },
    // Upload profile image
    uploadProfileImage: async (file)=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        const formData = new FormData();
        formData.append('image', file);
        return api.post('/api/accounts/profiles/me/upload_profile_image/', formData, {
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'multipart/form-data'
            }
        }).then((response)=>response.data);
    },
    // Upload resume using new Resume model
    uploadResume: async (file, name = null, isPrimary = false)=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        const formData = new FormData();
        formData.append('file', file);
        if (name) {
            formData.append('name', name);
        }
        formData.append('is_primary', isPrimary);
        return api.post('/api/accounts/profiles/me/resumes/', formData, {
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'multipart/form-data'
            }
        }).then((response)=>response.data);
    },
    // Admin upload resume for specific student
    adminUploadResume: async (studentId, file, name = null, isPrimary = false)=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        if (!token) {
            throw new Error('No authentication token found');
        }
        const formData = new FormData();
        formData.append('file', file);
        if (name) {
            formData.append('name', name);
        }
        formData.append('is_primary', isPrimary);
        return api.post(`/api/accounts/profiles/${studentId}/upload_resume/`, formData, {
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'multipart/form-data'
            }
        }).then((response)=>response.data);
    },
    // Admin get resumes for specific student
    adminGetResumes: async (studentId)=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        if (!token) {
            throw new Error('No authentication token found');
        }
        return api.get(`/api/accounts/profiles/${studentId}/resumes/`, {
            headers: {
                Authorization: `Bearer ${token}`
            }
        }).then((response)=>response.data);
    },
    // Admin upload certificate for specific student
    adminUploadCertificate: async (studentId, file, type)=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        if (!token) {
            throw new Error('No authentication token found');
        }
        const formData = new FormData();
        formData.append('file', file);
        formData.append('type', type);
        return api.post(`/api/accounts/profiles/${studentId}/upload_certificate/`, formData, {
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'multipart/form-data'
            }
        }).then((response)=>response.data);
    },
    // Admin upload semester marksheet for specific student
    adminUploadSemesterMarksheet: async (studentId, file, semester, cgpa)=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        if (!token) {
            throw new Error('No authentication token found');
        }
        const formData = new FormData();
        formData.append('marksheet_file', file);
        formData.append('semester', semester);
        formData.append('cgpa', cgpa);
        return api.post(`/api/accounts/profiles/${studentId}/upload_semester_marksheet/`, formData, {
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'multipart/form-data'
            }
        }).then((response)=>response.data);
    },
    // Legacy resume upload (for backward compatibility)
    uploadResumeToProfile: async (file)=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        const formData = new FormData();
        formData.append('resume', file);
        return api.patch('/api/auth/profile/', formData, {
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'multipart/form-data'
            }
        }).then((response)=>response.data);
    },
    // Get all resumes for the student
    getResumes: async ()=>{
        try {
            const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
            if (!token) {
                console.log('No authentication token, returning empty array');
                return [];
            }
            // Try the new resume endpoint first
            const response = await api.get('/api/accounts/profiles/me/resumes/', {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });
            // Ensure we're getting a proper response
            if (!response.data) {
                return await studentsAPI.getResumesLegacy();
            }
            // Handle different response formats
            if (Array.isArray(response.data)) {
                return response.data;
            } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
                return response.data.data;
            } else {
                console.log('Response data is not an array, trying fallback. Response:', response.data);
                try {
                    return await studentsAPI.getResumesLegacy();
                } catch (fallbackError) {
                    console.log('Fallback also failed, returning empty array');
                    return [];
                }
            }
        } catch (error) {
            console.log('Resume endpoint failed, using fallback method');
            try {
                return await studentsAPI.getResumesLegacy();
            } catch (fallbackError) {
                console.log('Fallback method also failed, returning empty array');
                return [];
            }
        }
    },
    // Legacy method to get resumes from profile
    getResumesLegacy: async ()=>{
        try {
            const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
            if (!token) {
                console.log('No auth token for legacy resume fetch');
                return [];
            }
            const profile = await studentsAPI.getProfile();
            if (profile?.resume || profile?.resume_url) {
                const resumeUrl = profile.resume_url || profile.resume;
                if (resumeUrl && resumeUrl.trim() !== '' && resumeUrl !== 'null' && resumeUrl !== 'undefined') {
                    const fileName = resumeUrl.split('/').pop() || 'Resume.pdf';
                    return [
                        {
                            id: profile.id || 1,
                            name: fileName,
                            file_url: resumeUrl,
                            uploaded_at: profile.updated_at || new Date().toISOString()
                        }
                    ];
                }
            }
            return [];
        } catch (error) {
            console.log('Legacy resume fetch error:', error.message);
            return [];
        }
    },
    // Delete a specific resume
    deleteResume: async (resumeId)=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        try {
            console.log(`Attempting to delete resume with ID: ${resumeId}`);
            // Use the new Resume model endpoint
            const response = await api.delete(`/api/accounts/profiles/me/resumes/${resumeId}/`, {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });
            console.log('DELETE resume successful:', response.data);
            return response.data;
        } catch (error) {
            console.error('Error deleting resume:', error);
            throw error;
        }
    },
    // Legacy delete function with fallback strategies
    deleteResumeLegacy: async (resumeId)=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        try {
            console.log(`Attempting to delete resume with ID: ${resumeId}`);
            let success = false;
            // Attempt different deletion strategies
            const strategies = [
                // Strategy 1: Standard DELETE request
                async ()=>{
                    try {
                        const response = await api.delete(`/api/accounts/profiles/me/resumes/${resumeId}/`, {
                            headers: {
                                Authorization: `Bearer ${token}`
                            }
                        });
                        console.log('DELETE resume successful:', response.data);
                        return {
                            success: true,
                            data: response.data
                        };
                    } catch (error) {
                        console.log(`Strategy 1 failed: ${error.message}`);
                        return {
                            success: false
                        };
                    }
                },
                // Strategy 2: POST to remove endpoint
                async ()=>{
                    try {
                        const response = await api.post(`/api/accounts/profiles/me/resumes/${resumeId}/remove/`, {}, {
                            headers: {
                                Authorization: `Bearer ${token}`
                            }
                        });
                        console.log('POST remove successful:', response.data);
                        return {
                            success: true,
                            data: response.data
                        };
                    } catch (error) {
                        console.log(`Strategy 2 failed: ${error.message}`);
                        return {
                            success: false
                        };
                    }
                },
                // Strategy 3: Patch profile with delete_resume field
                async ()=>{
                    try {
                        const response = await api.patch('/api/auth/profile/', {
                            delete_resume: resumeId
                        }, {
                            headers: {
                                Authorization: `Bearer ${token}`
                            }
                        });
                        console.log('PATCH profile successful:', response.data);
                        return {
                            success: true,
                            data: response.data
                        };
                    } catch (error) {
                        console.log(`Strategy 3 failed: ${error.message}`);
                        return {
                            success: false
                        };
                    }
                },
                // Strategy 4: Reset all resumes (extreme fallback)
                async ()=>{
                    try {
                        const response = await api.patch('/api/auth/profile/', {
                            reset_resumes: true
                        }, {
                            headers: {
                                Authorization: `Bearer ${token}`
                            }
                        });
                        console.log('Reset resumes successful:', response.data);
                        return {
                            success: true,
                            data: response.data
                        };
                    } catch (error) {
                        console.log(`Strategy 4 failed: ${error.message}`);
                        return {
                            success: false
                        };
                    }
                }
            ];
            // Try each strategy in sequence until one succeeds
            for (const strategy of strategies){
                const result = await strategy();
                if (result.success) {
                    success = true;
                    break;
                }
            }
            // Clear any locally cached data for this resume regardless of backend success
            if ("TURBOPACK compile-time truthy", 1) {
                // Clear any resume-related data from localStorage
                try {
                    const localStorageKeys = Object.keys(localStorage);
                    const resumeKeys = localStorageKeys.filter((key)=>key.includes('resume') || key.includes('file') || key.includes('document'));
                    if (resumeKeys.length > 0) {
                        console.log('Clearing resume-related localStorage items:', resumeKeys);
                        resumeKeys.forEach((key)=>localStorage.removeItem(key));
                    }
                    // Also try to clear specific keys that might be used for caching
                    localStorage.removeItem('resume_cache');
                    localStorage.removeItem('resume_list');
                    localStorage.removeItem('profile_cache');
                    localStorage.removeItem('resume_count');
                    localStorage.removeItem('last_resume_update');
                } catch (e) {
                    console.error('Error clearing localStorage:', e);
                }
            }
            return {
                success,
                message: success ? "Resume deleted successfully" : "Resume deleted locally but server sync failed"
            };
        } catch (error) {
            console.error('Resume deletion failed:', error.response?.status, error.message);
            // For UI purposes, return a success response even if backend fails
            // This allows the UI to remove the resume entry and maintain a good user experience
            return {
                success: true,
                synced: false,
                error: error.message,
                status: error.response?.status,
                message: "Resume removed from display (sync with server failed)"
            };
        }
    },
    // Upload certificate (10th or 12th)
    uploadCertificate: async (file, type)=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        const formData = new FormData();
        formData.append('file', file); // Backend expects 'file', not 'certificate'
        formData.append('type', type);
        return api.post('/api/accounts/profiles/me/upload_certificate/', formData, {
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'multipart/form-data'
            }
        }).then((response)=>response.data);
    },
    // Get all certificates for the student
    getCertificates: async ()=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        if (!token) {
            throw new Error('Authentication required to fetch certificates');
        }
        try {
            const response = await api.get('/api/accounts/profiles/me/certificates/', {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });
            // Ensure we're getting a proper response
            if (!response.data) {
                console.error('Empty response when fetching certificates');
                return [];
            }
            // Handle different response formats
            if (Array.isArray(response.data)) {
                return response.data;
            } else if (response.data.data && Array.isArray(response.data.data)) {
                return response.data.data;
            } else {
                console.error('Unexpected certificate data format:', response.data);
                return [];
            }
        } catch (error) {
            console.error('Certificate fetch error:', error.response?.status, error.message);
            throw error;
        }
    },
    // Delete a specific certificate
    deleteCertificate: async (certificateType)=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        try {
            console.log(`Attempting to delete certificate: ${certificateType}`);
            const response = await api.delete(`/api/accounts/profiles/me/delete_certificate/${certificateType}/`, {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });
            console.log('DELETE certificate successful:', response.data);
            return response.data;
        } catch (error) {
            console.error('Error deleting certificate:', error);
            throw error;
        }
    },
    // Admin delete certificate for specific student
    adminDeleteCertificate: async (studentId, certificateType)=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        try {
            console.log(`Admin attempting to delete certificate: ${certificateType} for student: ${studentId}`);
            const response = await api.delete(`/api/accounts/profiles/${studentId}/delete_certificate/${certificateType}/`, {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });
            console.log('Admin DELETE certificate successful:', response.data);
            return response.data;
        } catch (error) {
            console.error('Error deleting certificate:', error);
            throw error;
        }
    },
    // Delete a specific marksheet
    deleteMarksheet: async (semester)=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        try {
            console.log(`Attempting to delete marksheet for semester: ${semester}`);
            const response = await api.delete(`/api/accounts/profiles/me/delete_marksheet/${semester}/`, {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });
            console.log('DELETE marksheet successful:', response.data);
            return response.data;
        } catch (error) {
            console.error('Error deleting marksheet:', error);
            throw error;
        }
    },
    // Admin delete marksheet for specific student
    adminDeleteMarksheet: async (studentId, semester)=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        try {
            console.log(`Admin attempting to delete marksheet for semester: ${semester} for student: ${studentId}`);
            const response = await api.delete(`/api/accounts/profiles/${studentId}/delete_marksheet/${semester}/`, {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });
            console.log('Admin DELETE marksheet successful:', response.data);
            return response.data;
        } catch (error) {
            console.error('Error deleting marksheet:', error);
            throw error;
        }
    },
    // Legacy delete function (keeping for backward compatibility)
    deleteCertificateLegacy: async (certificateId)=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        try {
            console.log(`Attempting to delete certificate with ID: ${certificateId}`);
            let success = false;
            // Attempt different deletion strategies
            const strategies = [
                // Strategy 1: Standard DELETE request
                async ()=>{
                    try {
                        const response = await api.delete(`/api/accounts/profiles/me/certificates/${certificateId}/`, {
                            headers: {
                                Authorization: `Bearer ${token}`
                            }
                        });
                        console.log('DELETE certificate successful:', response.data);
                        return {
                            success: true,
                            data: response.data
                        };
                    } catch (error) {
                        console.log(`Strategy 1 failed: ${error.message}`);
                        return {
                            success: false
                        };
                    }
                },
                // Strategy 2: POST to remove endpoint
                async ()=>{
                    try {
                        const response = await api.post(`/api/accounts/profiles/me/certificates/${certificateId}/remove/`, {}, {
                            headers: {
                                Authorization: `Bearer ${token}`
                            }
                        });
                        console.log('POST remove successful:', response.data);
                        return {
                            success: true,
                            data: response.data
                        };
                    } catch (error) {
                        console.log(`Strategy 2 failed: ${error.message}`);
                        return {
                            success: false
                        };
                    }
                },
                // Strategy 3: Patch profile with delete_certificate field
                async ()=>{
                    try {
                        const response = await api.patch('/api/auth/profile/', {
                            delete_certificate: certificateId
                        }, {
                            headers: {
                                Authorization: `Bearer ${token}`
                            }
                        });
                        console.log('PATCH profile successful:', response.data);
                        return {
                            success: true,
                            data: response.data
                        };
                    } catch (error) {
                        console.log(`Strategy 3 failed: ${error.message}`);
                        return {
                            success: false
                        };
                    }
                },
                // Strategy 4: Reset all certificates (extreme fallback)
                async ()=>{
                    try {
                        const response = await api.patch('/api/auth/profile/', {
                            reset_certificates: true
                        }, {
                            headers: {
                                Authorization: `Bearer ${token}`
                            }
                        });
                        console.log('Reset certificates successful:', response.data);
                        return {
                            success: true,
                            data: response.data
                        };
                    } catch (error) {
                        console.log(`Strategy 4 failed: ${error.message}`);
                        return {
                            success: false
                        };
                    }
                }
            ];
            // Try each strategy in sequence until one succeeds
            for (const strategy of strategies){
                const result = await strategy();
                if (result.success) {
                    success = true;
                    break;
                }
            }
            // Clear any locally cached data for this certificate regardless of backend success
            if ("TURBOPACK compile-time truthy", 1) {
                // Clear any certificate-related data from localStorage
                try {
                    const localStorageKeys = Object.keys(localStorage);
                    const certificateKeys = localStorageKeys.filter((key)=>key.includes('certificate') || key.includes('document') || key.includes('cert'));
                    if (certificateKeys.length > 0) {
                        console.log('Clearing certificate-related localStorage items:', certificateKeys);
                        certificateKeys.forEach((key)=>localStorage.removeItem(key));
                    }
                    // Also try to clear specific keys that might be used for caching
                    localStorage.removeItem('certificate_cache');
                    localStorage.removeItem('certificate_list');
                    localStorage.removeItem('profile_cache');
                } catch (e) {
                    console.error('Error clearing localStorage:', e);
                }
            }
            return {
                success,
                message: success ? "Certificate deleted successfully" : "Certificate deleted locally but server sync failed"
            };
        } catch (error) {
            console.error('Certificate deletion failed:', error.response?.status, error.message);
            // For UI purposes, return a success response even if backend fails
            // This allows the UI to remove the certificate entry and maintain a good user experience
            return {
                success: true,
                synced: false,
                error: error.message,
                status: error.response?.status,
                message: "Certificate removed from display (sync with server failed)"
            };
        }
    },
    // Get semester marksheets
    getSemesterMarksheets: async ()=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        return api.get('/api/accounts/profiles/me/semester_marksheets/', {
            headers: {
                Authorization: `Bearer ${token}`
            }
        }).then((response)=>response.data);
    },
    // Upload semester marksheet
    uploadSemesterMarksheet: async (file, semester, cgpa)=>{
        const token = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAuthToken"])();
        const formData = new FormData();
        formData.append('marksheet_file', file);
        formData.append('semester', semester);
        formData.append('cgpa', cgpa);
        return api.post('/api/accounts/profiles/me/upload_semester_marksheet/', formData, {
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'multipart/form-data'
            }
        }).then((response)=>response.data);
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/profile/ResumeModal.jsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ResumeModal)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/fa/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$students$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/api/students.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
function ResumeModal({ isOpen, onClose, resume, onUpload, onDelete }) {
    _s();
    const [resumes, setResumes] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [uploading, setUploading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const fileInputRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Fetch resumes from backend when modal opens
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ResumeModal.useEffect": ()=>{
            if (isOpen) {
                fetchResumes();
            }
        }
    }["ResumeModal.useEffect"], [
        isOpen
    ]);
    const fetchResumes = async ()=>{
        try {
            setLoading(true);
            // Check if user is authenticated before proceeding
            const token = localStorage.getItem('access_token');
            if (!token) {
                console.error('No authentication token found');
                setResumes([]);
                setLoading(false);
                return;
            }
            // Try to fetch resumes from the new API endpoint with explicit user context
            let resumesData = [];
            try {
                console.log('Fetching user-specific resumes...');
                resumesData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$students$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["studentsAPI"].getResumes();
                console.log('Resumes fetched:', resumesData);
                // Verify we have user-specific data
                if (!Array.isArray(resumesData)) {
                    console.error('Invalid resume data format:', resumesData);
                    throw new Error('Invalid resume data format');
                }
            } catch (apiError) {
                console.log('New resumes API not available, falling back to profile data:', apiError);
                // Fallback: try to get resume from profile
                try {
                    const profile = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$students$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["studentsAPI"].getProfile();
                    console.log('User profile fetched for resume fallback:', profile?.id);
                    if (profile?.resume || profile?.resume_url) {
                        const resumeUrl = profile.resume_url || profile.resume;
                        const fileName = resumeUrl.split('/').pop() || 'Resume.pdf';
                        resumesData = [
                            {
                                id: profile.id || 1,
                                name: fileName,
                                resume_url: resumeUrl,
                                uploaded_at: profile.updated_at || new Date().toISOString()
                            }
                        ];
                    }
                } catch (profileError) {
                    console.error('Error fetching profile for resume:', profileError);
                }
            }
            // Transform backend data to frontend format
            const transformedResumes = resumesData.map((resume, index)=>({
                    id: resume.id || index + 1,
                    name: resume.name || resume.file_name || resume.resume_url?.split('/').pop() || `Resume ${index + 1}`,
                    date: resume.uploaded_at ? new Date(resume.uploaded_at).toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric',
                        year: 'numeric'
                    }) : new Date().toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric',
                        year: 'numeric'
                    }),
                    url: resume.resume_url || resume.file_url || resume.url,
                    status: 'success'
                }));
            console.log(`Displaying ${transformedResumes.length} resumes for current user`);
            setResumes(transformedResumes);
        } catch (error) {
            console.error('Error fetching resumes:', error);
            // Final fallback to using the resume prop if API fails
            if (resume) {
                const resumeArray = [];
                if (typeof resume === 'string' && resume.trim() !== '') {
                    const fileNameParts = resume.split('/');
                    const fileName = fileNameParts[fileNameParts.length - 1];
                    resumeArray.push({
                        id: 1,
                        name: fileName || "Resume",
                        date: new Date().toLocaleDateString('en-US', {
                            month: 'short',
                            day: 'numeric',
                            year: 'numeric'
                        }),
                        url: resume,
                        status: 'success'
                    });
                }
                setResumes(resumeArray);
            } else {
                // Set empty array if no fallback data
                setResumes([]);
            }
        } finally{
            setLoading(false);
        }
    };
    const handleUpload = async (e)=>{
        const file = e.target.files[0];
        if (file) {
            try {
                setUploading(true);
                // Verify file size (5MB limit)
                if (file.size > 5 * 1024 * 1024) {
                    alert('File size exceeds 5MB limit. Please select a smaller file.');
                    setUploading(false);
                    return;
                }
                // Create a new resume object with initial "uploading" status
                const newResume = {
                    id: Date.now(),
                    name: file.name,
                    date: new Date().toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric',
                        year: 'numeric'
                    }),
                    file: file,
                    url: URL.createObjectURL(file),
                    status: 'uploading',
                    progress: 0
                };
                // Add the new resume to the existing list
                setResumes((prevResumes)=>[
                        ...prevResumes,
                        newResume
                    ]);
                // Simulate progress updates
                const progressInterval = setInterval(()=>{
                    setResumes((prevResumes)=>prevResumes.map((r)=>r.id === newResume.id ? {
                                ...r,
                                progress: Math.min(r.progress + 25, 99)
                            } : r));
                }, 500);
                try {
                    // Upload the file to the server using the new API
                    await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$students$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["studentsAPI"].uploadResume(file, file.name, false);
                    // Clear interval and refresh the resumes list
                    clearInterval(progressInterval);
                    // Refresh resumes from backend (non-blocking)
                    fetchResumes().catch((err)=>console.log('Resume refresh failed after upload:', err));
                } catch (error) {
                    clearInterval(progressInterval);
                    setResumes((prevResumes)=>prevResumes.map((r)=>r.id === newResume.id ? {
                                ...r,
                                status: 'error',
                                progress: 0
                            } : r));
                    throw error;
                }
                setUploading(false);
            } catch (error) {
                console.error('Error uploading resume:', error);
                setUploading(false);
                alert('Failed to upload resume. Please try again.');
            }
        }
    };
    const handleViewResume = (url)=>{
        if (!url) {
            alert('Resume URL is not available');
            return;
        }
        // Special handling for different URL types
        if (url.startsWith('blob:')) {
            // Blob URLs should be used as is
            window.open(url, '_blank');
        } else if (!url.startsWith('http://') && !url.startsWith('https://')) {
            // Handle relative URLs by prepending the origin
            const fullUrl = `${window.location.origin}${url.startsWith('/') ? '' : '/'}${url}`;
            window.open(fullUrl, '_blank');
        } else {
            // Absolute URLs can be used directly
            window.open(url, '_blank');
        }
    };
    const handleSave = async (resume)=>{
        try {
            // Create a download link for the resume
            if (resume.url) {
                const link = document.createElement('a');
                link.href = resume.url;
                link.download = resume.name || 'resume.pdf';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            } else {
                alert('Resume file is not available for download');
            }
        } catch (error) {
            console.error('Error downloading resume:', error);
            alert('Failed to download resume. Please try again.');
        }
    };
    const handleDelete = async (id)=>{
        try {
            const resumeToDelete = resumes.find((r)=>r.id === id);
            // Remove from local state immediately for better UX
            setResumes((prevResumes)=>prevResumes.filter((resume)=>resume.id !== id));
            // Call backend delete if resume has a valid backend ID
            if (resumeToDelete && resumeToDelete.id && typeof resumeToDelete.id === 'number') {
                try {
                    const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$students$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["studentsAPI"].deleteResume(resumeToDelete.id);
                    console.log('Resume deletion response:', result);
                    // Even if the server returns an error, we'll keep the UI updated
                    // The important thing is the user experience - they expect the file to be gone
                    // Clear any local storage cache that might contain resume data
                    if ("TURBOPACK compile-time truthy", 1) {
                        try {
                            // Clear all resume-related data from localStorage
                            const localStorageKeys = Object.keys(localStorage);
                            const resumeKeys = localStorageKeys.filter((key)=>key.includes('resume') || key.includes('file') || key.includes('document'));
                            // Log the keys we're removing for debugging
                            if (resumeKeys.length > 0) {
                                console.log('Clearing resume-related localStorage items:', resumeKeys);
                                resumeKeys.forEach((key)=>localStorage.removeItem(key));
                            }
                            // Also clear some specific caches that might be used
                            localStorage.removeItem('resume_count');
                            localStorage.removeItem('last_resume_update');
                            // Update the user profile cache to remove the resume if applicable
                            const profileCache = localStorage.getItem('user_profile');
                            if (profileCache) {
                                try {
                                    const profile = JSON.parse(profileCache);
                                    if (profile && profile.resume) {
                                        profile.resume = null;
                                        localStorage.setItem('user_profile', JSON.stringify(profile));
                                    }
                                } catch (e) {
                                // Ignore JSON parse errors
                                }
                            }
                        } catch (e) {
                            console.error('Error clearing localStorage:', e);
                        }
                    }
                } catch (error) {
                    console.error('Backend delete failed, but UI is updated:', error);
                }
            }
            // Call the onDelete callback if provided
            if (typeof onDelete === 'function') {
                try {
                    await onDelete(resumeToDelete);
                } catch (callbackError) {
                    console.error('onDelete callback error:', callbackError);
                }
            }
            // Always force a refresh of the list regardless of success/failure
            // This ensures we're showing the correct state
            fetchResumes().catch((err)=>console.log('Resume refresh failed after delete:', err));
        } catch (error) {
            console.error('Error in delete process:', error);
            // Refresh the list to ensure UI is in sync with backend
            fetchResumes().catch((err)=>console.log('Resume refresh failed after delete error:', err));
        }
    };
    // Helper to render resume status icon
    const renderStatusIcon = (resume)=>{
        if (resume.status === 'uploading') {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "ml-2 text-blue-500",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaSpinner"], {
                    className: "animate-spin"
                }, void 0, false, {
                    fileName: "[project]/src/app/profile/ResumeModal.jsx",
                    lineNumber: 304,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/profile/ResumeModal.jsx",
                lineNumber: 303,
                columnNumber: 9
            }, this);
        } else if (resume.status === 'success') {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "ml-2 text-green-500",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaCheckCircle"], {}, void 0, false, {
                    fileName: "[project]/src/app/profile/ResumeModal.jsx",
                    lineNumber: 310,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/profile/ResumeModal.jsx",
                lineNumber: 309,
                columnNumber: 9
            }, this);
        } else if (resume.status === 'error') {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "ml-2 text-red-500",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaExclamationCircle"], {}, void 0, false, {
                    fileName: "[project]/src/app/profile/ResumeModal.jsx",
                    lineNumber: 316,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/profile/ResumeModal.jsx",
                lineNumber: 315,
                columnNumber: 9
            }, this);
        }
        return null;
    };
    if (!isOpen) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "fixed inset-0 flex items-center justify-center z-50 p-4",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "bg-white rounded-lg w-full max-w-2xl shadow-xl",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex justify-between items-center p-6 border-b",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                    className: "text-xl font-semibold text-gray-800",
                                    children: "My Resumes"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/profile/ResumeModal.jsx",
                                    lineNumber: 330,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-sm text-gray-500",
                                    children: "Upload multiple resumes for different job types"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/profile/ResumeModal.jsx",
                                    lineNumber: 331,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/profile/ResumeModal.jsx",
                            lineNumber: 329,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: onClose,
                            className: "text-gray-500 hover:text-gray-700",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaTimesCircle"], {
                                size: 24
                            }, void 0, false, {
                                fileName: "[project]/src/app/profile/ResumeModal.jsx",
                                lineNumber: 337,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/profile/ResumeModal.jsx",
                            lineNumber: 333,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/profile/ResumeModal.jsx",
                    lineNumber: 328,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "p-6 max-h-96 overflow-y-auto",
                    children: loading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-center py-8",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaSpinner"], {
                                className: "animate-spin text-blue-500 text-2xl mr-3"
                            }, void 0, false, {
                                fileName: "[project]/src/app/profile/ResumeModal.jsx",
                                lineNumber: 344,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-gray-600",
                                children: "Loading resumes..."
                            }, void 0, false, {
                                fileName: "[project]/src/app/profile/ResumeModal.jsx",
                                lineNumber: 345,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/profile/ResumeModal.jsx",
                        lineNumber: 343,
                        columnNumber: 13
                    }, this) : resumes.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-center py-8 text-gray-500",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                children: "No resumes uploaded yet"
                            }, void 0, false, {
                                fileName: "[project]/src/app/profile/ResumeModal.jsx",
                                lineNumber: 349,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-sm mt-2",
                                children: "You can upload multiple resumes for different job applications"
                            }, void 0, false, {
                                fileName: "[project]/src/app/profile/ResumeModal.jsx",
                                lineNumber: 350,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/profile/ResumeModal.jsx",
                        lineNumber: 348,
                        columnNumber: 13
                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex justify-between items-center mb-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "text-md font-medium text-gray-700",
                                        children: [
                                            "Your Resumes (",
                                            resumes.length,
                                            ")"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/profile/ResumeModal.jsx",
                                        lineNumber: 355,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-sm text-gray-500",
                                        children: resumes.length > 1 ? "You can use different resumes for different applications" : ""
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/profile/ResumeModal.jsx",
                                        lineNumber: 356,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/profile/ResumeModal.jsx",
                                lineNumber: 354,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "space-y-4",
                                children: resumes.map((resume)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center justify-between bg-gray-50 p-4 rounded-lg",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center flex-grow cursor-pointer hover:bg-gray-100 p-2 rounded",
                                                onClick: ()=>resume.status !== 'uploading' ? handleViewResume(resume.url) : null,
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "bg-blue-100 p-3 rounded-full mr-4",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaFileAlt"], {
                                                            className: "text-blue-600"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/profile/ResumeModal.jsx",
                                                            lineNumber: 371,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/profile/ResumeModal.jsx",
                                                        lineNumber: 370,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex-grow",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex items-center",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                                        className: "font-medium text-gray-800 mr-2",
                                                                        children: resume.name
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/profile/ResumeModal.jsx",
                                                                        lineNumber: 375,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    renderStatusIcon(resume),
                                                                    resume.status !== 'uploading' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaExternalLinkAlt"], {
                                                                        className: "text-gray-500 text-xs ml-2"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/profile/ResumeModal.jsx",
                                                                        lineNumber: 377,
                                                                        columnNumber: 61
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/profile/ResumeModal.jsx",
                                                                lineNumber: 374,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                className: "text-sm text-gray-500",
                                                                children: [
                                                                    "Uploaded on ",
                                                                    resume.date
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/profile/ResumeModal.jsx",
                                                                lineNumber: 379,
                                                                columnNumber: 25
                                                            }, this),
                                                            resume.status === 'uploading' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "w-full bg-gray-200 rounded-full h-2.5 mt-2",
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "bg-blue-600 h-2.5 rounded-full transition-all duration-300",
                                                                    style: {
                                                                        width: `${resume.progress}%`
                                                                    }
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/profile/ResumeModal.jsx",
                                                                    lineNumber: 384,
                                                                    columnNumber: 29
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/profile/ResumeModal.jsx",
                                                                lineNumber: 383,
                                                                columnNumber: 27
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/profile/ResumeModal.jsx",
                                                        lineNumber: 373,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/profile/ResumeModal.jsx",
                                                lineNumber: 366,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center space-x-2",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        onClick: ()=>handleSave(resume),
                                                        className: "p-2 text-green-500 hover:text-green-700 hover:bg-green-50 rounded-full",
                                                        "aria-label": "Download resume",
                                                        disabled: resume.status === 'uploading',
                                                        title: "Download resume",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaSave"], {
                                                            className: resume.status === 'uploading' ? 'opacity-50' : ''
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/profile/ResumeModal.jsx",
                                                            lineNumber: 401,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/profile/ResumeModal.jsx",
                                                        lineNumber: 394,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        onClick: ()=>handleDelete(resume.id),
                                                        className: "p-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full",
                                                        "aria-label": "Delete resume",
                                                        disabled: resume.status === 'uploading',
                                                        title: "Delete resume",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaTrash"], {
                                                            className: resume.status === 'uploading' ? 'opacity-50' : ''
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/profile/ResumeModal.jsx",
                                                            lineNumber: 412,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/profile/ResumeModal.jsx",
                                                        lineNumber: 405,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/profile/ResumeModal.jsx",
                                                lineNumber: 392,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, resume.id, true, {
                                        fileName: "[project]/src/app/profile/ResumeModal.jsx",
                                        lineNumber: 362,
                                        columnNumber: 19
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/src/app/profile/ResumeModal.jsx",
                                lineNumber: 360,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/profile/ResumeModal.jsx",
                        lineNumber: 353,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/profile/ResumeModal.jsx",
                    lineNumber: 341,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "border-t p-6 flex justify-between items-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-sm text-gray-500",
                                    children: "Supported formats: PDF, DOCX (max 5MB)"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/profile/ResumeModal.jsx",
                                    lineNumber: 424,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-xs text-gray-400 mt-1",
                                    children: "You can upload multiple resumes tailored to different positions"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/profile/ResumeModal.jsx",
                                    lineNumber: 427,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/profile/ResumeModal.jsx",
                            lineNumber: 423,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                    type: "file",
                                    accept: ".pdf,.docx",
                                    className: "hidden",
                                    ref: fileInputRef,
                                    onChange: handleUpload,
                                    disabled: uploading
                                }, void 0, false, {
                                    fileName: "[project]/src/app/profile/ResumeModal.jsx",
                                    lineNumber: 432,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>fileInputRef.current.click(),
                                    className: `bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center ${uploading ? 'opacity-70 cursor-not-allowed' : ''}`,
                                    disabled: uploading,
                                    children: uploading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaSpinner"], {
                                                className: "mr-2 animate-spin"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/profile/ResumeModal.jsx",
                                                lineNumber: 447,
                                                columnNumber: 19
                                            }, this),
                                            " Uploading..."
                                        ]
                                    }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaUpload"], {
                                                className: "mr-2"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/profile/ResumeModal.jsx",
                                                lineNumber: 451,
                                                columnNumber: 19
                                            }, this),
                                            " Add Resume"
                                        ]
                                    }, void 0, true)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/profile/ResumeModal.jsx",
                                    lineNumber: 440,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/profile/ResumeModal.jsx",
                            lineNumber: 431,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/profile/ResumeModal.jsx",
                    lineNumber: 422,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/profile/ResumeModal.jsx",
            lineNumber: 327,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/profile/ResumeModal.jsx",
        lineNumber: 326,
        columnNumber: 5
    }, this);
} //     )}
 //   </button>
 // </div>
_s(ResumeModal, "gE8USfkROY2BJfSm2XK3gRt3KBY=");
_c = ResumeModal;
var _c;
__turbopack_context__.k.register(_c, "ResumeModal");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/profile/DocumentsModal.jsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>DocumentsModal)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/fa/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
function DocumentsModal({ isOpen, onClose, documents = {}, onUploadCertificate, onUploadMarksheet, onDeleteCertificate, onDeleteMarksheet }) {
    _s();
    const [activeTab, setActiveTab] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('tenth');
    const [uploading, setUploading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [documentState, setDocumentState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        tenth: [],
        twelfth: [],
        semesterwise: []
    });
    const fileInputRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const semesterRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const cgpaRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Function to format URLs properly
    const getFormattedUrl = (url)=>{
        if (!url) return null;
        // Check if URL is relative (doesn't start with http)
        if (url && !url.startsWith('http')) {
            // Prepend the base URL for local development
            return `http://localhost:8000${url}`;
        }
        return url;
    };
    // Initialize with documents from backend if available
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DocumentsModal.useEffect": ()=>{
            const newState = {
                tenth: [],
                twelfth: [],
                semesterwise: []
            };
            // Format 10th certificate - only if it actually exists and is not empty
            if (documents.tenth && documents.tenth.trim() !== '' && documents.tenth !== 'null' && documents.tenth !== 'undefined') {
                const fileNameParts = typeof documents.tenth === 'string' ? documents.tenth.split('/') : [
                    '10th Certificate'
                ];
                const fileName = fileNameParts[fileNameParts.length - 1];
                // Only add if we have a valid filename
                if (fileName && fileName !== '' && fileName !== 'null') {
                    newState.tenth = [
                        {
                            id: 1,
                            name: fileName,
                            date: new Date().toLocaleDateString('en-US', {
                                month: 'short',
                                day: 'numeric',
                                year: 'numeric'
                            }),
                            url: documents.tenth
                        }
                    ];
                }
            }
            // Format 12th certificate - only if it actually exists and is not empty
            if (documents.twelfth && documents.twelfth.trim() !== '' && documents.twelfth !== 'null' && documents.twelfth !== 'undefined') {
                const fileNameParts = typeof documents.twelfth === 'string' ? documents.twelfth.split('/') : [
                    '12th Certificate'
                ];
                const fileName = fileNameParts[fileNameParts.length - 1];
                // Only add if we have a valid filename
                if (fileName && fileName !== '' && fileName !== 'null') {
                    newState.twelfth = [
                        {
                            id: 1,
                            name: fileName,
                            date: new Date().toLocaleDateString('en-US', {
                                month: 'short',
                                day: 'numeric',
                                year: 'numeric'
                            }),
                            url: documents.twelfth
                        }
                    ];
                }
            }
            // Format semester marksheets - only if they actually exist
            if (documents.semesterMarksheets && Array.isArray(documents.semesterMarksheets) && documents.semesterMarksheets.length > 0) {
                newState.semesterwise = documents.semesterMarksheets.filter({
                    "DocumentsModal.useEffect": (sheet)=>sheet && sheet.marksheet_url && sheet.marksheet_url.trim() !== ''
                }["DocumentsModal.useEffect"]).map({
                    "DocumentsModal.useEffect": (sheet)=>({
                            id: sheet.id,
                            name: `Semester ${sheet.semester} Marksheet (CGPA: ${sheet.cgpa})`,
                            date: sheet.upload_date ? new Date(sheet.upload_date).toLocaleDateString('en-US', {
                                month: 'short',
                                day: 'numeric',
                                year: 'numeric'
                            }) : 'Unknown date',
                            url: sheet.marksheet_url || sheet.marksheet_file,
                            semester: sheet.semester,
                            cgpa: sheet.cgpa
                        })
                }["DocumentsModal.useEffect"]);
            }
            setDocumentState(newState);
        }
    }["DocumentsModal.useEffect"], [
        documents
    ]);
    const handleUpload = async (e)=>{
        const file = e.target.files[0];
        if (!file) return;
        try {
            setUploading(true);
            if (activeTab === 'tenth') {
                await onUploadCertificate(file, 'tenth');
                const newDoc = {
                    id: Date.now(),
                    name: file.name,
                    date: new Date().toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric',
                        year: 'numeric'
                    }),
                    url: URL.createObjectURL(file) // Temporary URL for preview
                };
                setDocumentState((prev)=>({
                        ...prev,
                        tenth: [
                            newDoc
                        ]
                    }));
            } else if (activeTab === 'twelfth') {
                await onUploadCertificate(file, 'twelfth');
                const newDoc = {
                    id: Date.now(),
                    name: file.name,
                    date: new Date().toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric',
                        year: 'numeric'
                    }),
                    url: URL.createObjectURL(file) // Temporary URL for preview
                };
                setDocumentState((prev)=>({
                        ...prev,
                        twelfth: [
                            newDoc
                        ]
                    }));
            } else if (activeTab === 'semesterwise') {
                // Safety check for refs
                if (!semesterRef.current || !cgpaRef.current) {
                    alert('Semester input fields are not available');
                    setUploading(false);
                    return;
                }
                const semester = semesterRef.current.value;
                const cgpa = cgpaRef.current.value;
                if (!semester || !cgpa) {
                    alert('Please enter semester number and CGPA');
                    setUploading(false);
                    return;
                }
                await onUploadMarksheet(file, semester, cgpa);
                const newDoc = {
                    id: Date.now(),
                    name: `Semester ${semester} Marksheet (CGPA: ${cgpa})`,
                    date: new Date().toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric',
                        year: 'numeric'
                    }),
                    url: URL.createObjectURL(file),
                    semester,
                    cgpa
                };
                setDocumentState((prev)=>({
                        ...prev,
                        semesterwise: [
                            ...prev.semesterwise,
                            newDoc
                        ]
                    }));
            }
            setUploading(false);
        } catch (error) {
            console.error('Error uploading document:', error);
            setUploading(false);
            alert('Failed to upload document. Please try again.');
        }
    };
    const handleViewDocument = (url)=>{
        if (!url) {
            alert('Document URL is not available');
            return;
        }
        const formattedUrl = getFormattedUrl(url);
        window.open(formattedUrl, '_blank');
    };
    const handleSaveDocument = (document)=>{
        try {
            if (document.url) {
                const formattedUrl = getFormattedUrl(document.url);
                const link = document.createElement('a');
                link.href = formattedUrl;
                link.download = document.name || 'document.pdf';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            } else {
                alert('Document file is not available for download');
            }
        } catch (error) {
            console.error('Error downloading document:', error);
            alert('Failed to download document. Please try again.');
        }
    };
    const handleDelete = async (id, documentType = null)=>{
        try {
            if (activeTab === 'tenth' || activeTab === 'twelfth') {
                // Delete certificate
                const certType = activeTab === 'tenth' ? '10th' : '12th';
                await onDeleteCertificate(certType);
                // Update local state
                setDocumentState((prev)=>({
                        ...prev,
                        [activeTab]: []
                    }));
            } else if (activeTab === 'semesterwise' && documentType) {
                // Delete marksheet
                await onDeleteMarksheet(documentType.semester);
                // Update local state
                setDocumentState((prev)=>({
                        ...prev,
                        semesterwise: prev.semesterwise.filter((doc)=>doc.semester !== documentType.semester)
                    }));
            } else {
                // Fallback to local state update only
                setDocumentState((prev)=>({
                        ...prev,
                        [activeTab]: prev[activeTab].filter((doc)=>doc.id !== id)
                    }));
            }
        } catch (error) {
            console.error('Error deleting document:', error);
            alert('Failed to delete document. Please try again.');
        }
    };
    if (!isOpen) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "fixed inset-0 flex items-center justify-center z-50 p-4",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "bg-white rounded-lg w-full max-w-2xl shadow-xl",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex justify-between items-center p-6 border-b",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                            className: "text-xl font-semibold text-gray-800",
                            children: "My Documents"
                        }, void 0, false, {
                            fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                            lineNumber: 239,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: onClose,
                            className: "text-gray-500 hover:text-gray-700",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaTimesCircle"], {
                                size: 24
                            }, void 0, false, {
                                fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                                lineNumber: 244,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                            lineNumber: 240,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                    lineNumber: 238,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex border-b",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            className: `px-6 py-3 text-sm font-medium ${activeTab === 'tenth' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700'}`,
                            onClick: ()=>setActiveTab('tenth'),
                            children: "10th Certificate"
                        }, void 0, false, {
                            fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                            lineNumber: 250,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            className: `px-6 py-3 text-sm font-medium ${activeTab === 'twelfth' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700'}`,
                            onClick: ()=>setActiveTab('twelfth'),
                            children: "12th Certificate"
                        }, void 0, false, {
                            fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                            lineNumber: 256,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            className: `px-6 py-3 text-sm font-medium ${activeTab === 'semesterwise' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700'}`,
                            onClick: ()=>setActiveTab('semesterwise'),
                            children: "Semester Grades"
                        }, void 0, false, {
                            fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                            lineNumber: 262,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                    lineNumber: 249,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "p-6 max-h-96 overflow-y-auto",
                    children: documentState[activeTab].length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-center py-8 text-gray-500",
                        children: "No documents uploaded for this category yet"
                    }, void 0, false, {
                        fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                        lineNumber: 272,
                        columnNumber: 13
                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-4",
                        children: documentState[activeTab].map((document, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center justify-between bg-gray-50 p-4 rounded-lg",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center flex-grow cursor-pointer hover:bg-gray-100 p-2 rounded",
                                        onClick: ()=>handleViewDocument(document.url),
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "bg-blue-100 p-3 rounded-full mr-4",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaFileAlt"], {
                                                    className: "text-blue-600"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                                                    lineNumber: 287,
                                                    columnNumber: 23
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                                                lineNumber: 286,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                                className: "font-medium text-gray-800 mr-2",
                                                                children: document.name
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                                                                lineNumber: 291,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaExternalLinkAlt"], {
                                                                className: "text-gray-500 text-xs"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                                                                lineNumber: 292,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                                                        lineNumber: 290,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "text-sm text-gray-500",
                                                        children: [
                                                            "Uploaded on ",
                                                            document.date
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                                                        lineNumber: 294,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                                                lineNumber: 289,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                                        lineNumber: 282,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center space-x-2",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                onClick: ()=>handleSaveDocument(document),
                                                className: "p-2 text-green-500 hover:text-green-700 hover:bg-green-50 rounded-full",
                                                "aria-label": "Download document",
                                                title: "Download document",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaSave"], {}, void 0, false, {
                                                    fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                                                    lineNumber: 305,
                                                    columnNumber: 23
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                                                lineNumber: 299,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                onClick: ()=>handleDelete(document.id, document),
                                                className: "p-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full",
                                                "aria-label": "Delete document",
                                                title: "Delete document",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaTrash"], {}, void 0, false, {
                                                    fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                                                    lineNumber: 315,
                                                    columnNumber: 23
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                                                lineNumber: 309,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                                        lineNumber: 297,
                                        columnNumber: 19
                                    }, this)
                                ]
                            }, document.id || `${activeTab}-${index}`, true, {
                                fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                                lineNumber: 278,
                                columnNumber: 17
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                        lineNumber: 276,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                    lineNumber: 270,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "border-t p-6",
                    children: [
                        activeTab === 'semesterwise' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mb-4 grid grid-cols-2 gap-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                            className: "block text-sm font-medium text-gray-700 mb-1",
                                            children: "Semester Number"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                                            lineNumber: 329,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                            type: "number",
                                            min: "1",
                                            max: "8",
                                            ref: semesterRef,
                                            className: "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",
                                            placeholder: "e.g., 1"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                                            lineNumber: 332,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                                    lineNumber: 328,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                            className: "block text-sm font-medium text-gray-700 mb-1",
                                            children: "CGPA"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                                            lineNumber: 342,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                            type: "number",
                                            step: "0.01",
                                            min: "0",
                                            max: "10",
                                            ref: cgpaRef,
                                            className: "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",
                                            placeholder: "e.g., 8.5"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                                            lineNumber: 345,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                                    lineNumber: 341,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                            lineNumber: 327,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex justify-between items-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-sm text-gray-500",
                                    children: "Supported formats: PDF, JPG, PNG (max 5MB)"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                                    lineNumber: 359,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                            type: "file",
                                            accept: ".pdf,.jpg,.jpeg,.png",
                                            className: "hidden",
                                            ref: fileInputRef,
                                            onChange: handleUpload,
                                            disabled: uploading
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                                            lineNumber: 363,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: ()=>fileInputRef.current.click(),
                                            className: `bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center ${uploading ? 'opacity-70 cursor-not-allowed' : ''}`,
                                            disabled: uploading,
                                            children: uploading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaSpinner"], {
                                                        className: "mr-2 animate-spin"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                                                        lineNumber: 378,
                                                        columnNumber: 21
                                                    }, this),
                                                    " Uploading..."
                                                ]
                                            }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaUpload"], {
                                                        className: "mr-2"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                                                        lineNumber: 382,
                                                        columnNumber: 21
                                                    }, this),
                                                    " Upload Document"
                                                ]
                                            }, void 0, true)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                                            lineNumber: 371,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                                    lineNumber: 362,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                            lineNumber: 358,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/profile/DocumentsModal.jsx",
                    lineNumber: 324,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/profile/DocumentsModal.jsx",
            lineNumber: 237,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/profile/DocumentsModal.jsx",
        lineNumber: 236,
        columnNumber: 5
    }, this);
}
_s(DocumentsModal, "uPx6qpqpBanhecI7rj8XdqfDejI=");
_c = DocumentsModal;
var _c;
__turbopack_context__.k.register(_c, "DocumentsModal");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/utils/fileValidation.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// File validation utilities
__turbopack_context__.s({
    "FILE_CONSTRAINTS": (()=>FILE_CONSTRAINTS),
    "createFileUploadPreview": (()=>createFileUploadPreview),
    "default": (()=>__TURBOPACK__default__export__),
    "formatFileSize": (()=>formatFileSize),
    "getFileErrorMessage": (()=>getFileErrorMessage),
    "getFileTypeIcon": (()=>getFileTypeIcon),
    "validateFile": (()=>validateFile),
    "validateMultipleFiles": (()=>validateMultipleFiles)
});
const FILE_CONSTRAINTS = {
    resume: {
        maxSize: 5 * 1024 * 1024,
        allowedTypes: [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ],
        allowedExtensions: [
            '.pdf',
            '.doc',
            '.docx'
        ],
        displayName: 'Resume'
    },
    profile_image: {
        maxSize: 2 * 1024 * 1024,
        allowedTypes: [
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/webp'
        ],
        allowedExtensions: [
            '.jpg',
            '.jpeg',
            '.png',
            '.gif',
            '.webp'
        ],
        displayName: 'Profile Image'
    },
    certificate: {
        maxSize: 5 * 1024 * 1024,
        allowedTypes: [
            'application/pdf',
            'image/jpeg',
            'image/png'
        ],
        allowedExtensions: [
            '.pdf',
            '.jpg',
            '.jpeg',
            '.png'
        ],
        displayName: 'Certificate'
    },
    marksheet: {
        maxSize: 5 * 1024 * 1024,
        allowedTypes: [
            'application/pdf',
            'image/jpeg',
            'image/png'
        ],
        allowedExtensions: [
            '.pdf',
            '.jpg',
            '.jpeg',
            '.png'
        ],
        displayName: 'Marksheet'
    },
    cover_letter: {
        maxSize: 2 * 1024 * 1024,
        allowedTypes: [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ],
        allowedExtensions: [
            '.pdf',
            '.doc',
            '.docx'
        ],
        displayName: 'Cover Letter'
    },
    generic_document: {
        maxSize: 10 * 1024 * 1024,
        allowedTypes: [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'image/jpeg',
            'image/png'
        ],
        allowedExtensions: [
            '.pdf',
            '.doc',
            '.docx',
            '.jpg',
            '.jpeg',
            '.png'
        ],
        displayName: 'Document'
    }
};
const validateFile = (file, fileType = 'generic_document')=>{
    const constraints = FILE_CONSTRAINTS[fileType];
    const errors = [];
    const warnings = [];
    if (!file) {
        return {
            isValid: false,
            errors: [
                'No file selected'
            ],
            warnings: [],
            file: null
        };
    }
    // Check file size
    if (file.size > constraints.maxSize) {
        const maxSizeMB = Math.round(constraints.maxSize / (1024 * 1024));
        const fileSizeMB = Math.round(file.size / (1024 * 1024) * 10) / 10;
        errors.push(`File size (${fileSizeMB}MB) exceeds maximum allowed size of ${maxSizeMB}MB`);
    }
    // Check file type
    if (!constraints.allowedTypes.includes(file.type)) {
        const allowedTypesDisplay = constraints.allowedExtensions.join(', ');
        errors.push(`File type not supported. Allowed formats: ${allowedTypesDisplay}`);
    }
    // Check file extension
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
    if (!constraints.allowedExtensions.includes(fileExtension)) {
        const allowedExtensionsDisplay = constraints.allowedExtensions.join(', ');
        errors.push(`File extension not allowed. Supported extensions: ${allowedExtensionsDisplay}`);
    }
    // Additional validations based on file type
    switch(fileType){
        case 'resume':
            if (file.size < 50 * 1024) {
                warnings.push('Resume file seems very small. Please ensure it contains adequate content.');
            }
            if (file.name.length > 100) {
                warnings.push('File name is very long. Consider using a shorter name.');
            }
            break;
        case 'profile_image':
            if (file.size < 10 * 1024) {
                warnings.push('Image file seems very small. Please ensure it\'s a clear photo.');
            }
            break;
        case 'certificate':
        case 'marksheet':
            if (file.size < 100 * 1024) {
                warnings.push('Document file seems small. Please ensure it\'s clearly readable.');
            }
            break;
    }
    // Check for potentially malicious file names
    if (file.name.includes('..') || file.name.includes('/') || file.name.includes('\\')) {
        errors.push('File name contains invalid characters');
    }
    // Check for very long file names
    if (file.name.length > 255) {
        errors.push('File name is too long (maximum 255 characters)');
    }
    return {
        isValid: errors.length === 0,
        errors,
        warnings,
        file,
        size: file.size,
        sizeDisplay: formatFileSize(file.size),
        type: file.type,
        name: file.name
    };
};
const validateMultipleFiles = (files, fileType = 'generic_document', maxFiles = 5)=>{
    const results = [];
    const overallErrors = [];
    if (!files || files.length === 0) {
        return {
            isValid: false,
            errors: [
                'No files selected'
            ],
            warnings: [],
            results: [],
            totalSize: 0
        };
    }
    if (files.length > maxFiles) {
        overallErrors.push(`Too many files selected. Maximum allowed: ${maxFiles}`);
    }
    let totalSize = 0;
    let hasErrors = false;
    for(let i = 0; i < files.length; i++){
        const validation = validateFile(files[i], fileType);
        results.push({
            index: i,
            fileName: files[i].name,
            ...validation
        });
        totalSize += files[i].size;
        if (!validation.isValid) {
            hasErrors = true;
        }
    }
    // Check total size for multiple files
    const maxTotalSize = FILE_CONSTRAINTS[fileType].maxSize * Math.min(files.length, maxFiles);
    if (totalSize > maxTotalSize) {
        const maxTotalSizeMB = Math.round(maxTotalSize / (1024 * 1024));
        const totalSizeMB = Math.round(totalSize / (1024 * 1024) * 10) / 10;
        overallErrors.push(`Total file size (${totalSizeMB}MB) exceeds maximum allowed (${maxTotalSizeMB}MB)`);
        hasErrors = true;
    }
    return {
        isValid: !hasErrors && overallErrors.length === 0,
        errors: overallErrors,
        warnings: [],
        results,
        totalSize,
        totalSizeDisplay: formatFileSize(totalSize),
        fileCount: files.length
    };
};
const formatFileSize = (bytes)=>{
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = [
        'Bytes',
        'KB',
        'MB',
        'GB'
    ];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};
const getFileTypeIcon = (fileType)=>{
    const iconMap = {
        'application/pdf': 'file-text',
        'application/msword': 'file-text',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'file-text',
        'image/jpeg': 'image',
        'image/png': 'image',
        'image/gif': 'image',
        'image/webp': 'image'
    };
    return iconMap[fileType] || 'file';
};
const createFileUploadPreview = (file, validation)=>{
    return {
        name: file.name,
        size: formatFileSize(file.size),
        type: file.type,
        icon: getFileTypeIcon(file.type),
        isValid: validation.isValid,
        errors: validation.errors,
        warnings: validation.warnings,
        preview: file.type.startsWith('image/') ? URL.createObjectURL(file) : null
    };
};
const getFileErrorMessage = (validationResult)=>{
    if (validationResult.isValid) {
        return null;
    }
    const primaryError = validationResult.errors[0];
    if (primaryError.includes('size')) {
        return {
            title: 'File Too Large',
            message: primaryError,
            suggestion: 'Please compress the file or choose a smaller file.',
            type: 'size'
        };
    }
    if (primaryError.includes('type') || primaryError.includes('extension')) {
        return {
            title: 'Unsupported File Format',
            message: primaryError,
            suggestion: 'Please convert your file to a supported format.',
            type: 'format'
        };
    }
    if (primaryError.includes('name')) {
        return {
            title: 'Invalid File Name',
            message: primaryError,
            suggestion: 'Please rename your file and try again.',
            type: 'name'
        };
    }
    return {
        title: 'File Upload Error',
        message: primaryError,
        suggestion: 'Please check your file and try again.',
        type: 'generic'
    };
};
const __TURBOPACK__default__export__ = {
    validateFile,
    validateMultipleFiles,
    formatFileSize,
    getFileTypeIcon,
    createFileUploadPreview,
    getFileErrorMessage,
    FILE_CONSTRAINTS
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/profile/page.jsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ProfilePage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/fa/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$profile$2f$ResumeModal$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/profile/ResumeModal.jsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$profile$2f$DocumentsModal$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/profile/DocumentsModal.jsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$students$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/api/students.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$NotificationContext$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/NotificationContext.jsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$fileValidation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/fileValidation.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
function ProfilePage() {
    _s();
    const { handleApiError, showFileUploadError } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$NotificationContext$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useNotification"])();
    const [isResumeModalOpen, setIsResumeModalOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isDocumentsModalOpen, setIsDocumentsModalOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [profile, setProfile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [semesterMarksheets, setSemesterMarksheets] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [resumeCount, setResumeCount] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const [lastResumeUpdate, setLastResumeUpdate] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [companyStats, setCompanyStats] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        totalListings: 0,
        eligibleJobs: 0,
        loading: true
    });
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ProfilePage.useEffect": ()=>{
            async function fetchProfileData() {
                try {
                    setLoading(true);
                    const profileData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$students$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["studentsAPI"].getProfile();
                    setProfile(profileData);
                    // Fetch semester marksheets
                    const marksheets = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$students$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["studentsAPI"].getSemesterMarksheets();
                    setSemesterMarksheets(marksheets);
                    // Fetch resume count
                    await fetchResumeCount();
                    // Fetch company stats
                    fetchCompanyStats();
                    setLoading(false);
                } catch (err) {
                    console.error('Error fetching profile:', err);
                    handleApiError(err, 'loading profile');
                    setError('Failed to load profile data. Please try again later.');
                    setLoading(false);
                }
            }
            fetchProfileData();
        }
    }["ProfilePage.useEffect"], []);
    // Function to fetch resume count
    const fetchResumeCount = async ()=>{
        try {
            const resumes = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$students$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["studentsAPI"].getResumes();
            setResumeCount(resumes.length);
            if (resumes.length > 0) {
                // Get the most recent upload date
                const mostRecent = resumes.reduce((latest, resume)=>{
                    const resumeDate = new Date(resume.uploaded_at || resume.created_at);
                    const latestDate = new Date(latest);
                    return resumeDate > latestDate ? resume.uploaded_at || resume.created_at : latest;
                }, resumes[0].uploaded_at || resumes[0].created_at);
                setLastResumeUpdate(mostRecent);
            }
        } catch (err) {
            console.error('Error fetching resumes:', err);
            // Fallback to profile resume if available
            if (profile?.resume) {
                setResumeCount(1);
                setLastResumeUpdate(profile.updated_at);
            }
        }
    };
    // Function to fetch company statistics
    const fetchCompanyStats = async ()=>{
        try {
            // Import and use API functions from companies.js
            const { getCompanyStats, fetchCompanies } = await __turbopack_context__.r("[project]/src/api/companies.js [app-client] (ecmascript, async loader)")(__turbopack_context__.i);
            // Get stats if available
            let stats;
            try {
                const statsResponse = await getCompanyStats();
                stats = statsResponse.data || statsResponse;
            } catch (error) {
                console.log('Could not fetch company stats, calculating from companies data');
                // Fetch all companies to get total count
                const companies = await fetchCompanies();
                stats = {
                    total: companies.length,
                    active_jobs: companies.reduce((sum, company)=>sum + (company.totalActiveJobs || 0), 0)
                };
            }
            // Calculate eligible jobs based on profile criteria (e.g., CGPA requirements)
            const eligibleJobsCount = calculateEligibleJobs(stats, profile);
            setCompanyStats({
                totalListings: stats.total || 0,
                eligibleJobs: eligibleJobsCount,
                loading: false
            });
        } catch (error) {
            console.error('Error fetching company stats:', error);
            setCompanyStats({
                totalListings: 0,
                eligibleJobs: 0,
                loading: false
            });
        }
    };
    // Helper function to calculate eligible jobs based on profile
    const calculateEligibleJobs = (companies, profile)=>{
        if (!companies || !profile) return 0;
        // Get user's CGPA for comparison
        const userCgpa = parseFloat(getOverallCGPA());
        // Get user's branch/major for matching
        const userBranch = profile.branch;
        // Count jobs that match the user's criteria
        let eligibleCount = 0;
        // For each company, check eligibility
        companies.forEach((company)=>{
            // In a real implementation, we would check each job's requirements
            // For now, use a simple heuristic based on company tier
            const companyJobCount = company.totalActiveJobs || 0;
            let eligibilityPercent = 0;
            // Very basic eligibility logic (would be replaced with actual requirements)
            if (userCgpa >= 8.5) {
                eligibilityPercent = 0.9; // 90% of jobs eligible for high CGPA students
            } else if (userCgpa >= 7.5) {
                eligibilityPercent = 0.75; // 75% eligible for good CGPA students
            } else if (userCgpa >= 6.5) {
                eligibilityPercent = 0.5; // 50% eligible for average CGPA students
            } else {
                eligibilityPercent = 0.25; // 25% eligible for below average CGPA students
            }
            // Add to eligible count
            eligibleCount += Math.floor(companyJobCount * eligibilityPercent);
        });
        return eligibleCount;
    };
    // Function to handle profile image upload
    const handleProfileImageUpload = async (file)=>{
        // Validate file first
        const validation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$fileValidation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["validateFile"])(file, 'profile_image');
        if (!validation.isValid) {
            showFileUploadError(validation.errors);
            return;
        }
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$students$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["studentsAPI"].uploadProfileImage(file);
            // Refresh profile data
            const profileData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$students$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["studentsAPI"].getProfile();
            setProfile(profileData);
        } catch (err) {
            console.error('Error uploading profile image:', err);
            showFileUploadError([
                'Failed to upload profile image',
                'Please check the file format and size',
                'Supported formats: JPG, PNG, GIF (max 2MB)'
            ]);
        }
    };
    // Function to handle resume upload
    const handleResumeUpload = async (file)=>{
        // Validate file first
        const validation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$fileValidation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["validateFile"])(file, 'resume');
        if (!validation.isValid) {
            showFileUploadError(validation.errors);
            return;
        }
        try {
            // Use the new resume upload API
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$students$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["studentsAPI"].uploadResume(file, file.name, false);
            // Refresh resume count after upload
            await fetchResumeCount();
        } catch (err) {
            console.error('Error uploading resume:', err);
            showFileUploadError([
                'Failed to upload resume',
                'Please check the file format and size',
                'Supported formats: PDF, DOC, DOCX (max 5MB)'
            ]);
        }
    };
    // Function to handle resume delete
    const handleResumeDelete = async (resume)=>{
        try {
            // Clear any cached resume data
            if ("object" !== 'undefined' && resume?.id) {
                localStorage.removeItem(`resume_${resume.id}`);
                localStorage.removeItem('resume_count');
                localStorage.removeItem('last_resume_update');
            }
            // Force refresh resume count
            await fetchResumeCount();
            // If we were displaying a specific resume that was deleted, clear it
            if (resume?.url === profile?.resume) {
                const updatedProfile = {
                    ...profile,
                    resume: null
                };
                setProfile(updatedProfile);
            }
        } catch (error) {
            console.error('Error handling resume deletion:', error);
        }
    };
    // Get overall CGPA from database
    const getOverallCGPA = ()=>{
        return profile?.gpa || '0.00';
    };
    // Calculate percentage from CGPA (approximation)
    const calculatePercentage = (cgpa)=>{
        if (!cgpa || cgpa === '-') return '-';
        return (parseFloat(cgpa) * 9.5).toFixed(2) + '%';
    };
    // Helper function to check if a document actually exists
    const isValidDocument = (document)=>{
        return document && typeof document === 'string' && document.trim() !== '' && document !== 'null' && document !== 'undefined';
    };
    // Calculate actual document count
    const getDocumentCount = ()=>{
        const tenthCount = isValidDocument(profile?.tenth_certificate) ? 1 : 0;
        const twelfthCount = isValidDocument(profile?.twelfth_certificate) ? 1 : 0;
        const semesterCount = semesterMarksheets ? semesterMarksheets.filter((sheet)=>sheet && isValidDocument(sheet.marksheet_url)).length : 0;
        return tenthCount + twelfthCount + semesterCount;
    };
    // Get semester CGPA value
    const getSemesterCGPA = (semNumber)=>{
        if (!profile) return '-';
        const semesterCGPA = profile[`semester${semNumber}_cgpa`];
        return semesterCGPA || '-';
    };
    // Get semester marksheets sorted by semester number
    const getSortedSemesterMarksheets = ()=>{
        if (!semesterMarksheets) return [];
        return [
            ...semesterMarksheets
        ].sort((a, b)=>a.semester - b.semester);
    };
    // Format date to display period (e.g., "Sep 2021 - Aug 2025")
    const formatEducationPeriod = (startYear, endYear)=>{
        if (!startYear || !endYear) return '-';
        return `${startYear} - ${endYear}`;
    };
    // Function to display either the profile image or a fallback with initial
    const renderProfileImage = ()=>{
        if (loading) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "w-50 h-50 bg-blue-100 flex items-center justify-center rounded-lg mb-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaSpinner"], {
                    className: "animate-spin text-blue-500 text-2xl"
                }, void 0, false, {
                    fileName: "[project]/src/app/profile/page.jsx",
                    lineNumber: 283,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/profile/page.jsx",
                lineNumber: 282,
                columnNumber: 9
            }, this);
        }
        if (profile?.profile_image_url) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "w-50 h-50 bg-blue-100 object-center text-center rounded-lg mb-4 relative mx-auto",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    src: profile.profile_image_url,
                    alt: `${profile.first_name} ${profile.last_name}`,
                    fill: true,
                    className: "rounded-lg object-cover"
                }, void 0, false, {
                    fileName: "[project]/src/app/profile/page.jsx",
                    lineNumber: 291,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/profile/page.jsx",
                lineNumber: 290,
                columnNumber: 9
            }, this);
        }
        // Fallback to initial
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "w-50 h-50 bg-blue-500 text-white flex items-center justify-center rounded-lg mb-4 mx-auto",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                className: "text-3xl font-bold",
                children: profile?.initial || (profile?.first_name ? profile.first_name[0].toUpperCase() : 'S')
            }, void 0, false, {
                fileName: "[project]/src/app/profile/page.jsx",
                lineNumber: 304,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/profile/page.jsx",
            lineNumber: 303,
            columnNumber: 7
        }, this);
    };
    // Display loading state
    if (loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center justify-center min-h-screen bg-gray-50",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaSpinner"], {
                    className: "animate-spin text-blue-500 text-4xl mr-3"
                }, void 0, false, {
                    fileName: "[project]/src/app/profile/page.jsx",
                    lineNumber: 315,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                    className: "text-xl text-gray-700",
                    children: "Loading profile..."
                }, void 0, false, {
                    fileName: "[project]/src/app/profile/page.jsx",
                    lineNumber: 316,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/profile/page.jsx",
            lineNumber: 314,
            columnNumber: 7
        }, this);
    }
    // Display error state
    if (error) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center justify-center min-h-screen bg-gray-50",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-red-500 text-xl mb-4",
                        children: error
                    }, void 0, false, {
                        fileName: "[project]/src/app/profile/page.jsx",
                        lineNumber: 326,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>window.location.reload(),
                        className: "px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600",
                        children: "Retry"
                    }, void 0, false, {
                        fileName: "[project]/src/app/profile/page.jsx",
                        lineNumber: 327,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/profile/page.jsx",
                lineNumber: 325,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/profile/page.jsx",
            lineNumber: 324,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "bg-gray-50 min-h-screen p-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "max-w-8xl mx-auto grid grid-cols-1 lg:grid-cols-12 gap-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "lg:col-span-3 bg-white rounded-lg p-5 shadow-sm h-fit content-card profile-container",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex justify-between",
                                children: renderProfileImage()
                            }, void 0, false, {
                                fileName: "[project]/src/app/profile/page.jsx",
                                lineNumber: 343,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                className: "text-xl font-bold text-center mt-2 text-gray-800",
                                children: profile?.first_name && profile?.last_name ? `${profile.first_name} ${profile.last_name}` : '-'
                            }, void 0, false, {
                                fileName: "[project]/src/app/profile/page.jsx",
                                lineNumber: 347,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mt-4 space-y-3 text-md",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-gray-500 w-20",
                                                children: "Student ID"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 355,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "font-medium text-gray-800",
                                                children: [
                                                    ": ",
                                                    profile?.student_id || '-'
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 356,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/profile/page.jsx",
                                        lineNumber: 354,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-gray-500 w-20",
                                                children: "Major"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 359,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "font-medium text-gray-800",
                                                children: [
                                                    ": ",
                                                    profile?.branch || '-'
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 360,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/profile/page.jsx",
                                        lineNumber: 358,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-gray-500 w-20",
                                                children: "Passed Out"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 363,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "font-medium text-gray-800",
                                                children: [
                                                    ": ",
                                                    profile?.passout_year || '-'
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 364,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/profile/page.jsx",
                                        lineNumber: 362,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-gray-500 w-20",
                                                children: "Gender"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 367,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "font-medium text-gray-800",
                                                children: [
                                                    ": ",
                                                    profile?.gender || '-'
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 368,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/profile/page.jsx",
                                        lineNumber: 366,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-gray-500 w-20",
                                                children: "Birthday"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 371,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "font-medium text-gray-800",
                                                children: [
                                                    ": ",
                                                    profile?.date_of_birth || '-'
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 372,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/profile/page.jsx",
                                        lineNumber: 370,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-gray-500 w-20",
                                                children: "Phone"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 375,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "font-medium text-gray-800",
                                                children: [
                                                    ": ",
                                                    profile?.phone || '-'
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 376,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/profile/page.jsx",
                                        lineNumber: 374,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-gray-500 w-20",
                                                children: "Email"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 379,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "font-medium text-gray-800",
                                                children: [
                                                    ": ",
                                                    profile?.contact_email || profile?.user?.email || '-'
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 380,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/profile/page.jsx",
                                        lineNumber: 378,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-gray-500 w-20",
                                                children: "Campus"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 383,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "font-medium text-gray-800",
                                                children: [
                                                    ": ",
                                                    profile?.college_name || '-'
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 384,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/profile/page.jsx",
                                        lineNumber: 382,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-gray-500 w-20",
                                                children: "Placement"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 387,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "font-medium text-gray-800",
                                                children: [
                                                    ": ",
                                                    profile?.joining_year && profile?.passout_year ? `${profile.joining_year}-${profile.passout_year}` : '-'
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 388,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/profile/page.jsx",
                                        lineNumber: 386,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/profile/page.jsx",
                                lineNumber: 353,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mt-6",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "text-lg font-semibold mb-3 text-gray-800",
                                        children: "Skills"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/profile/page.jsx",
                                        lineNumber: 396,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex flex-wrap gap-2",
                                        children: (()=>{
                                            const skills = profile?.skills || selectedStudent?.skills;
                                            if (Array.isArray(skills) && skills.length > 0) {
                                                return skills.map((skill, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm",
                                                        children: skill
                                                    }, index, false, {
                                                        fileName: "[project]/src/app/profile/page.jsx",
                                                        lineNumber: 403,
                                                        columnNumber: 21
                                                    }, this));
                                            } else if (typeof skills === 'string' && skills.trim()) {
                                                return skills.split(',').map((skill, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm",
                                                        children: skill.trim()
                                                    }, index, false, {
                                                        fileName: "[project]/src/app/profile/page.jsx",
                                                        lineNumber: 412,
                                                        columnNumber: 21
                                                    }, this));
                                            } else {
                                                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-gray-600",
                                                    children: "No skills listed"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/profile/page.jsx",
                                                    lineNumber: 420,
                                                    columnNumber: 26
                                                }, this);
                                            }
                                        })()
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/profile/page.jsx",
                                        lineNumber: 397,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/profile/page.jsx",
                                lineNumber: 395,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/profile/page.jsx",
                        lineNumber: 342,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "lg:col-span-6 space-y-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-white rounded-lg p-5 shadow-sm content-card",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                        className: "text-xl font-semibold mb-6 text-gray-800",
                                        children: "Academic"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/profile/page.jsx",
                                        lineNumber: 432,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex justify-between items-center mb-3",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                        className: "text-lg font-semibold text-gray-800",
                                                        children: "Semester Wise score"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/profile/page.jsx",
                                                        lineNumber: 435,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "bg-blue-50 inline-flex items-center px-3 py-1 rounded-full ml-3",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "text-blue-600 font-medium",
                                                                children: getOverallCGPA()
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/profile/page.jsx",
                                                                lineNumber: 437,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "text-sm text-gray-500 ml-1",
                                                                children: "CGPA"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/profile/page.jsx",
                                                                lineNumber: 438,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "text-blue-600 ml-2",
                                                                children: calculatePercentage(getOverallCGPA())
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/profile/page.jsx",
                                                                lineNumber: 439,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/profile/page.jsx",
                                                        lineNumber: 436,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 434,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "text-lg font-semibold text-gray-800",
                                                children: formatEducationPeriod(profile?.joining_year, profile?.passout_year)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 442,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/profile/page.jsx",
                                        lineNumber: 433,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mb-6",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "overflow-x-auto",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("table", {
                                                className: "w-full border-collapse border border-gray-300",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("thead", {
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                                            className: "bg-gray-50",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                                    className: "border border-gray-300 px-4 py-3 text-sm font-semibold text-gray-700",
                                                                    children: "Sem"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/profile/page.jsx",
                                                                    lineNumber: 452,
                                                                    columnNumber: 23
                                                                }, this),
                                                                [
                                                                    1,
                                                                    2,
                                                                    3,
                                                                    4,
                                                                    5,
                                                                    6,
                                                                    7,
                                                                    8
                                                                ].map((sem)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                                        className: "border border-gray-300 px-4 py-3 text-sm font-semibold text-gray-700",
                                                                        children: sem
                                                                    }, sem, false, {
                                                                        fileName: "[project]/src/app/profile/page.jsx",
                                                                        lineNumber: 454,
                                                                        columnNumber: 25
                                                                    }, this))
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/profile/page.jsx",
                                                            lineNumber: 451,
                                                            columnNumber: 21
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/profile/page.jsx",
                                                        lineNumber: 450,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tbody", {
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                    className: "border border-gray-300 px-4 py-3 text-sm font-medium text-gray-700",
                                                                    children: "Cgpa"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/profile/page.jsx",
                                                                    lineNumber: 460,
                                                                    columnNumber: 23
                                                                }, this),
                                                                [
                                                                    1,
                                                                    2,
                                                                    3,
                                                                    4,
                                                                    5,
                                                                    6,
                                                                    7,
                                                                    8
                                                                ].map((sem)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                                        className: "border border-gray-300 px-4 py-3 text-sm text-gray-700",
                                                                        children: getSemesterCGPA(sem)
                                                                    }, sem, false, {
                                                                        fileName: "[project]/src/app/profile/page.jsx",
                                                                        lineNumber: 462,
                                                                        columnNumber: 25
                                                                    }, this))
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/profile/page.jsx",
                                                            lineNumber: 459,
                                                            columnNumber: 21
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/profile/page.jsx",
                                                        lineNumber: 458,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 449,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/profile/page.jsx",
                                            lineNumber: 448,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/profile/page.jsx",
                                        lineNumber: 447,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {
                                        className: "my-6"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/profile/page.jsx",
                                        lineNumber: 471,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mb-6",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex justify-between items-center mb-3",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                                className: "text-lg font-semibold text-gray-800",
                                                                children: "Class XII"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/profile/page.jsx",
                                                                lineNumber: 477,
                                                                columnNumber: 19
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "bg-blue-50 inline-flex items-center px-3 py-1 rounded-full ml-3",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "text-blue-600 font-medium",
                                                                        children: profile?.twelfth_cgpa || '-'
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/profile/page.jsx",
                                                                        lineNumber: 479,
                                                                        columnNumber: 21
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "text-sm text-gray-500 ml-1",
                                                                        children: "CGPA"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/profile/page.jsx",
                                                                        lineNumber: 480,
                                                                        columnNumber: 21
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "text-blue-600 ml-2",
                                                                        children: profile?.twelfth_percentage || '-'
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/profile/page.jsx",
                                                                        lineNumber: 481,
                                                                        columnNumber: 21
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/profile/page.jsx",
                                                                lineNumber: 478,
                                                                columnNumber: 19
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/profile/page.jsx",
                                                        lineNumber: 476,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-lg font-semibold text-gray-800",
                                                        children: profile?.twelfth_year_of_passing ? `${parseInt(profile.twelfth_year_of_passing) - 2} - ${profile.twelfth_year_of_passing}` : '-'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/profile/page.jsx",
                                                        lineNumber: 484,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 475,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex justify-between items-start mb-2",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "grid grid-cols-2 gap-6 w-full",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "space-y-3",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            className: "text-gray-500 text-sm w-[120px]",
                                                                            children: "College :"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/profile/page.jsx",
                                                                            lineNumber: 494,
                                                                            columnNumber: 23
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            className: "text-gray-700 font-medium",
                                                                            children: profile?.twelfth_school || '-'
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/profile/page.jsx",
                                                                            lineNumber: 495,
                                                                            columnNumber: 23
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/profile/page.jsx",
                                                                    lineNumber: 493,
                                                                    columnNumber: 21
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            className: "text-gray-500 text-sm w-[120px]",
                                                                            children: "Board :"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/profile/page.jsx",
                                                                            lineNumber: 498,
                                                                            columnNumber: 23
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            className: "text-gray-700 font-medium",
                                                                            children: profile?.twelfth_board || '-'
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/profile/page.jsx",
                                                                            lineNumber: 499,
                                                                            columnNumber: 23
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/profile/page.jsx",
                                                                    lineNumber: 497,
                                                                    columnNumber: 21
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/profile/page.jsx",
                                                            lineNumber: 492,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "space-y-3",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            className: "text-gray-500 text-sm w-[120px]",
                                                                            children: "Location :"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/profile/page.jsx",
                                                                            lineNumber: 504,
                                                                            columnNumber: 23
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            className: "text-gray-700 font-medium",
                                                                            children: profile?.city || '-'
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/profile/page.jsx",
                                                                            lineNumber: 505,
                                                                            columnNumber: 23
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/profile/page.jsx",
                                                                    lineNumber: 503,
                                                                    columnNumber: 21
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            className: "text-gray-500 text-sm w-[120px]",
                                                                            children: "Specialization :"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/profile/page.jsx",
                                                                            lineNumber: 508,
                                                                            columnNumber: 23
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            className: "text-gray-700 font-medium",
                                                                            children: profile?.twelfth_specialization || '-'
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/profile/page.jsx",
                                                                            lineNumber: 509,
                                                                            columnNumber: 23
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/profile/page.jsx",
                                                                    lineNumber: 507,
                                                                    columnNumber: 21
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/profile/page.jsx",
                                                            lineNumber: 502,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/profile/page.jsx",
                                                    lineNumber: 491,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 490,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/profile/page.jsx",
                                        lineNumber: 474,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {
                                        className: "my-6"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/profile/page.jsx",
                                        lineNumber: 520,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex justify-between items-center mb-3",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                                className: "text-lg font-semibold text-gray-800",
                                                                children: "Class X"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/profile/page.jsx",
                                                                lineNumber: 526,
                                                                columnNumber: 19
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "bg-blue-50 inline-flex items-center px-3 py-1 rounded-full ml-3",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "text-blue-600 font-medium",
                                                                        children: profile?.tenth_cgpa || '-'
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/profile/page.jsx",
                                                                        lineNumber: 528,
                                                                        columnNumber: 21
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "text-sm text-gray-500 ml-1",
                                                                        children: "CGPA"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/profile/page.jsx",
                                                                        lineNumber: 529,
                                                                        columnNumber: 21
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "text-blue-600 ml-2",
                                                                        children: profile?.tenth_percentage || '-'
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/profile/page.jsx",
                                                                        lineNumber: 530,
                                                                        columnNumber: 21
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/profile/page.jsx",
                                                                lineNumber: 527,
                                                                columnNumber: 19
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/profile/page.jsx",
                                                        lineNumber: 525,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-lg font-semibold text-gray-800",
                                                        children: profile?.tenth_year_of_passing ? `${parseInt(profile.tenth_year_of_passing) - 1} - ${profile.tenth_year_of_passing}` : '-'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/profile/page.jsx",
                                                        lineNumber: 533,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 524,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex justify-between items-start mb-2",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "grid grid-cols-2 gap-6 w-full",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "space-y-3",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            className: "text-gray-500 text-sm w-[120px]",
                                                                            children: "School :"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/profile/page.jsx",
                                                                            lineNumber: 544,
                                                                            columnNumber: 23
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            className: "text-gray-700 font-medium",
                                                                            children: profile?.tenth_school || '-'
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/profile/page.jsx",
                                                                            lineNumber: 545,
                                                                            columnNumber: 23
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/profile/page.jsx",
                                                                    lineNumber: 543,
                                                                    columnNumber: 21
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            className: "text-gray-500 text-sm w-[120px]",
                                                                            children: "Board :"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/profile/page.jsx",
                                                                            lineNumber: 548,
                                                                            columnNumber: 23
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            className: "text-gray-700 font-medium",
                                                                            children: profile?.tenth_board || '-'
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/profile/page.jsx",
                                                                            lineNumber: 549,
                                                                            columnNumber: 23
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/profile/page.jsx",
                                                                    lineNumber: 547,
                                                                    columnNumber: 21
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/profile/page.jsx",
                                                            lineNumber: 542,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "space-y-3",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            className: "text-gray-500 text-sm w-[120px]",
                                                                            children: "Location :"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/profile/page.jsx",
                                                                            lineNumber: 554,
                                                                            columnNumber: 23
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            className: "text-gray-700 font-medium",
                                                                            children: profile?.city || '-'
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/profile/page.jsx",
                                                                            lineNumber: 555,
                                                                            columnNumber: 23
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/profile/page.jsx",
                                                                    lineNumber: 553,
                                                                    columnNumber: 21
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            className: "text-gray-500 text-sm w-[120px]",
                                                                            children: "Specialization :"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/profile/page.jsx",
                                                                            lineNumber: 558,
                                                                            columnNumber: 23
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            className: "text-gray-700 font-medium",
                                                                            children: "-"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/profile/page.jsx",
                                                                            lineNumber: 559,
                                                                            columnNumber: 23
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/profile/page.jsx",
                                                                    lineNumber: 557,
                                                                    columnNumber: 21
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/profile/page.jsx",
                                                            lineNumber: 552,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/profile/page.jsx",
                                                    lineNumber: 541,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 540,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/profile/page.jsx",
                                        lineNumber: 523,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/profile/page.jsx",
                                lineNumber: 431,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-white rounded-lg p-5 shadow-sm content-card",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                        className: "text-xl font-semibold mb-4 text-gray-800",
                                        children: "Companies"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/profile/page.jsx",
                                        lineNumber: 569,
                                        columnNumber: 13
                                    }, this),
                                    companyStats.loading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center justify-center py-4",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaSpinner"], {
                                                className: "animate-spin text-blue-500 text-xl mr-2"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 573,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-gray-600",
                                                children: "Loading company data..."
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 574,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/profile/page.jsx",
                                        lineNumber: 572,
                                        columnNumber: 15
                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "mb-4",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center justify-between",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-gray-500 text-sm",
                                                            children: "Total Listings"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/profile/page.jsx",
                                                            lineNumber: 581,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-lg font-semibold text-gray-700",
                                                            children: companyStats.totalListings
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/profile/page.jsx",
                                                            lineNumber: 582,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/profile/page.jsx",
                                                    lineNumber: 580,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 579,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "mb-4",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center justify-between",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-gray-500 text-sm",
                                                            children: "Eligible Jobs"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/profile/page.jsx",
                                                            lineNumber: 589,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-lg font-semibold text-gray-700",
                                                            children: companyStats.eligibleJobs
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/profile/page.jsx",
                                                            lineNumber: 590,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/profile/page.jsx",
                                                    lineNumber: 588,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 587,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/profile/page.jsx",
                                lineNumber: 568,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/profile/page.jsx",
                        lineNumber: 429,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "lg:col-span-3 space-y-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-white rounded-lg p-5 shadow-sm content-card",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                        className: "text-xl font-semibold mb-4 text-gray-800",
                                        children: "My Files"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/profile/page.jsx",
                                        lineNumber: 602,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center p-2 mb-3 cursor-pointer hover:bg-gray-50 rounded-lg transition-colors",
                                        onClick: ()=>setIsResumeModalOpen(true),
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-3",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "text-blue-600",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                        xmlns: "http://www.w3.org/2000/svg",
                                                        className: "h-6 w-6",
                                                        fill: "none",
                                                        viewBox: "0 0 24 24",
                                                        stroke: "currentColor",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                            strokeLinecap: "round",
                                                            strokeLinejoin: "round",
                                                            strokeWidth: 2,
                                                            d: "M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/profile/page.jsx",
                                                            lineNumber: 611,
                                                            columnNumber: 21
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/profile/page.jsx",
                                                        lineNumber: 610,
                                                        columnNumber: 19
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/profile/page.jsx",
                                                    lineNumber: 609,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 608,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex-grow",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                        className: "font-medium text-gray-700",
                                                        children: "Resumes"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/profile/page.jsx",
                                                        lineNumber: 616,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "text-sm text-gray-500",
                                                        children: resumeCount > 0 ? `${resumeCount} resume${resumeCount > 1 ? 's' : ''} uploaded` + (lastResumeUpdate ? ` • Last updated ${new Date(lastResumeUpdate).toLocaleDateString()}` : '') : 'No resumes uploaded'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/profile/page.jsx",
                                                        lineNumber: 617,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 615,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "bg-green-50 px-3 py-1 rounded-full",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "text-green-600 font-medium",
                                                    children: resumeCount
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/profile/page.jsx",
                                                    lineNumber: 626,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 625,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/profile/page.jsx",
                                        lineNumber: 604,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center p-2 cursor-pointer hover:bg-gray-50 rounded-lg transition-colors",
                                        onClick: ()=>setIsDocumentsModalOpen(true),
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-3",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "text-blue-600",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                        xmlns: "http://www.w3.org/2000/svg",
                                                        className: "h-6 w-6",
                                                        fill: "none",
                                                        viewBox: "0 0 24 24",
                                                        stroke: "currentColor",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                            strokeLinecap: "round",
                                                            strokeLinejoin: "round",
                                                            strokeWidth: 2,
                                                            d: "M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/profile/page.jsx",
                                                            lineNumber: 637,
                                                            columnNumber: 21
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/profile/page.jsx",
                                                        lineNumber: 636,
                                                        columnNumber: 19
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/profile/page.jsx",
                                                    lineNumber: 635,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 634,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex-grow",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                        className: "font-medium text-gray-700",
                                                        children: "Documents"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/profile/page.jsx",
                                                        lineNumber: 642,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "text-sm text-gray-500",
                                                        children: "Academic certificates and marksheets"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/profile/page.jsx",
                                                        lineNumber: 643,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 641,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "bg-green-50 px-3 py-1 rounded-full",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "text-green-600 font-medium",
                                                    children: getDocumentCount()
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/profile/page.jsx",
                                                    lineNumber: 646,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 645,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/profile/page.jsx",
                                        lineNumber: 630,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/profile/page.jsx",
                                lineNumber: 601,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-white rounded-lg p-5 shadow-sm content-card",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex justify-between items-center mb-4",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                            className: "text-xl font-semibold text-gray-800",
                                            children: "CURRENT ADDRESS"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/profile/page.jsx",
                                            lineNumber: 656,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/profile/page.jsx",
                                        lineNumber: 655,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "space-y-3 text-sm",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "text-gray-500 w-20",
                                                        children: "City"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/profile/page.jsx",
                                                        lineNumber: 661,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "font-medium text-gray-700",
                                                        children: [
                                                            ": ",
                                                            profile?.city || '-'
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/profile/page.jsx",
                                                        lineNumber: 662,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 660,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "text-gray-500 w-20",
                                                        children: "District"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/profile/page.jsx",
                                                        lineNumber: 665,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "font-medium text-gray-700",
                                                        children: [
                                                            ": ",
                                                            profile?.district || '-'
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/profile/page.jsx",
                                                        lineNumber: 666,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 664,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "text-gray-500 w-20",
                                                        children: "State"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/profile/page.jsx",
                                                        lineNumber: 669,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "font-medium text-gray-700",
                                                        children: [
                                                            ": ",
                                                            profile?.state || '-'
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/profile/page.jsx",
                                                        lineNumber: 670,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 668,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "text-gray-500 w-20",
                                                        children: "Pin Code"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/profile/page.jsx",
                                                        lineNumber: 673,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "font-medium text-gray-700",
                                                        children: [
                                                            ": ",
                                                            profile?.pincode || '-'
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/profile/page.jsx",
                                                        lineNumber: 674,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 672,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "text-gray-500 w-20",
                                                        children: "Country"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/profile/page.jsx",
                                                        lineNumber: 677,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "font-medium text-gray-700",
                                                        children: [
                                                            ": ",
                                                            profile?.country || '-'
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/profile/page.jsx",
                                                        lineNumber: 678,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 676,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "text-gray-500 w-20",
                                                        children: "Address"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/profile/page.jsx",
                                                        lineNumber: 681,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "font-medium text-gray-700",
                                                        children: [
                                                            ": ",
                                                            profile?.address || '-'
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/profile/page.jsx",
                                                        lineNumber: 682,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/profile/page.jsx",
                                                lineNumber: 680,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/profile/page.jsx",
                                        lineNumber: 659,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/profile/page.jsx",
                                lineNumber: 654,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/profile/page.jsx",
                        lineNumber: 599,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/profile/page.jsx",
                lineNumber: 340,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$profile$2f$ResumeModal$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                isOpen: isResumeModalOpen,
                onClose: ()=>setIsResumeModalOpen(false),
                resume: profile?.resume_url || profile?.resume,
                onUpload: handleResumeUpload,
                onDelete: handleResumeDelete
            }, void 0, false, {
                fileName: "[project]/src/app/profile/page.jsx",
                lineNumber: 690,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$profile$2f$DocumentsModal$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                isOpen: isDocumentsModalOpen,
                onClose: ()=>setIsDocumentsModalOpen(false),
                documents: {
                    tenth: profile?.tenth_certificate_url || profile?.tenth_certificate,
                    twelfth: profile?.twelfth_certificate_url || profile?.twelfth_certificate,
                    semesterMarksheets: semesterMarksheets
                },
                onUploadCertificate: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$students$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["studentsAPI"].uploadCertificate,
                onUploadMarksheet: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$students$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["studentsAPI"].uploadSemesterMarksheet,
                onDeleteCertificate: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$students$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["studentsAPI"].deleteCertificate,
                onDeleteMarksheet: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$api$2f$students$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["studentsAPI"].deleteMarksheet
            }, void 0, false, {
                fileName: "[project]/src/app/profile/page.jsx",
                lineNumber: 699,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/profile/page.jsx",
        lineNumber: 339,
        columnNumber: 5
    }, this);
}
_s(ProfilePage, "rA6S5pkR/+oy6qD47zrd5k0lFzk=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$NotificationContext$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useNotification"]
    ];
});
_c = ProfilePage;
var _c;
__turbopack_context__.k.register(_c, "ProfilePage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_ed48a327._.js.map