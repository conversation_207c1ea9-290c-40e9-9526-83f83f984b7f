# Generated by Django 5.1.5 on 2025-07-01 22:15

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='MetricsCache',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('metric_type', models.CharField(choices=[('dashboard_stats', 'Dashboard Statistics'), ('company_stats', 'Company Statistics'), ('student_stats', 'Student Statistics'), ('job_stats', 'Job Statistics'), ('application_stats', 'Application Statistics'), ('placement_stats', 'Placement Statistics')], max_length=50)),
                ('metric_key', models.CharField(max_length=100)),
                ('data', models.JSONField()),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_valid', models.BooleanField(default=True)),
            ],
            options={
                'indexes': [models.Index(fields=['metric_type', 'metric_key'], name='metrics_met_metric__952049_idx'), models.Index(fields=['last_updated'], name='metrics_met_last_up_9b618c_idx'), models.Index(fields=['is_valid'], name='metrics_met_is_vali_c0ada2_idx')],
                'unique_together': {('metric_type', 'metric_key')},
            },
        ),
        migrations.CreateModel(
            name='PaginatedDataCache',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cache_type', models.CharField(choices=[('students_list', 'Students List'), ('companies_list', 'Companies List'), ('jobs_list', 'Jobs List'), ('applications_list', 'Applications List')], max_length=50)),
                ('filter_hash', models.CharField(max_length=64)),
                ('page_number', models.IntegerField()),
                ('page_size', models.IntegerField()),
                ('total_count', models.IntegerField()),
                ('data', models.JSONField()),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_valid', models.BooleanField(default=True)),
            ],
            options={
                'indexes': [models.Index(fields=['cache_type', 'filter_hash'], name='metrics_pag_cache_t_b740d9_idx'), models.Index(fields=['last_updated'], name='metrics_pag_last_up_c622b3_idx'), models.Index(fields=['is_valid'], name='metrics_pag_is_vali_779d70_idx')],
                'unique_together': {('cache_type', 'filter_hash', 'page_number', 'page_size')},
            },
        ),
    ]
