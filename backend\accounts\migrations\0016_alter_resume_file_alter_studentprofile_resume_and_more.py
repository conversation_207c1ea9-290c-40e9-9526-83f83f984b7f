# Generated by Django 5.1.5 on 2025-09-21 07:18

import accounts.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0015_resume'),
    ]

    operations = [
        migrations.AlterField(
            model_name='resume',
            name='file',
            field=models.FileField(help_text='Resume file', upload_to='resumes/', validators=[accounts.models.validate_resume_file]),
        ),
        migrations.AlterField(
            model_name='studentprofile',
            name='resume',
            field=models.FileField(blank=True, null=True, upload_to='resumes/', validators=[accounts.models.validate_resume_file]),
        ),
        migrations.AlterField(
            model_name='studentprofile',
            name='semester1_marksheet',
            field=models.FileField(blank=True, null=True, upload_to='marksheets/sem1/', validators=[accounts.models.validate_certificate_file]),
        ),
        migrations.AlterField(
            model_name='studentprofile',
            name='semester2_marksheet',
            field=models.FileField(blank=True, null=True, upload_to='marksheets/sem2/', validators=[accounts.models.validate_certificate_file]),
        ),
        migrations.AlterField(
            model_name='studentprofile',
            name='semester3_marksheet',
            field=models.FileField(blank=True, null=True, upload_to='marksheets/sem3/', validators=[accounts.models.validate_certificate_file]),
        ),
        migrations.AlterField(
            model_name='studentprofile',
            name='semester4_marksheet',
            field=models.FileField(blank=True, null=True, upload_to='marksheets/sem4/', validators=[accounts.models.validate_certificate_file]),
        ),
        migrations.AlterField(
            model_name='studentprofile',
            name='semester5_marksheet',
            field=models.FileField(blank=True, null=True, upload_to='marksheets/sem5/', validators=[accounts.models.validate_certificate_file]),
        ),
        migrations.AlterField(
            model_name='studentprofile',
            name='semester6_marksheet',
            field=models.FileField(blank=True, null=True, upload_to='marksheets/sem6/', validators=[accounts.models.validate_certificate_file]),
        ),
        migrations.AlterField(
            model_name='studentprofile',
            name='semester7_marksheet',
            field=models.FileField(blank=True, null=True, upload_to='marksheets/sem7/', validators=[accounts.models.validate_certificate_file]),
        ),
        migrations.AlterField(
            model_name='studentprofile',
            name='semester8_marksheet',
            field=models.FileField(blank=True, null=True, upload_to='marksheets/sem8/', validators=[accounts.models.validate_certificate_file]),
        ),
        migrations.AlterField(
            model_name='studentprofile',
            name='tenth_certificate',
            field=models.FileField(blank=True, null=True, upload_to='certificates/10th/', validators=[accounts.models.validate_certificate_file]),
        ),
        migrations.AlterField(
            model_name='studentprofile',
            name='twelfth_certificate',
            field=models.FileField(blank=True, null=True, upload_to='certificates/12th/', validators=[accounts.models.validate_certificate_file]),
        ),
    ]
