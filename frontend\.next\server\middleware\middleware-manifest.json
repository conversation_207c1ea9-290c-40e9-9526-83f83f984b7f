{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_adbadb87._.js", "server/edge/chunks/[root-of-the-server]__e07e566a._.js", "server/edge/chunks/edge-wrapper_9cb4b359.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "4L1N1ApV6JxRkywf2zSy/shy6TmjdExKn7foFPP8JTY=", "__NEXT_PREVIEW_MODE_ID": "532e53f8992b569df630be8e263680c1", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "106e3b7387b3cfc63b40dcb2ee5c13151107bf3414b3ee8732f00dad6c0a3d7d", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "68802b9bac274619291fae2ee0b94edb05fd234a7dec440c7f1239d2f41e3f48"}}}, "instrumentation": null, "functions": {}}