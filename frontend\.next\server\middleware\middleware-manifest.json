{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_adbadb87._.js", "server/edge/chunks/[root-of-the-server]__e07e566a._.js", "server/edge/chunks/edge-wrapper_9cb4b359.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "4L1N1ApV6JxRkywf2zSy/shy6TmjdExKn7foFPP8JTY=", "__NEXT_PREVIEW_MODE_ID": "84db1d380244eb1ba13d97f5099865cb", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "3246b422e914f45e9782ff33814dffdd201774a109b27e50afce58bcd8685444", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "6dbc8d6cb8760e77ca01732fd3b8ae05729ee86a70b204ebf4f3299988ba834a"}}}, "instrumentation": null, "functions": {}}