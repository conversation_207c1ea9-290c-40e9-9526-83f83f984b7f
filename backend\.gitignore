# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Django stuff:
*.log
local_settings.py
mydb.sqlite3
db.sqlite3
db.sqlite3-journal
media/
staticfiles/
static/

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
*.manifest
*.spec

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Environments
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# IDE
.idea/
.vscode/
*.sublime-project
*.sublime-workspace

# OS generated files
.DS_Store
.DS_Store?
._*
Thumbs.db

# Backup files
*.bak
*.orig
*.tmp

# Node.js (if applicable)
node_modules/
npm-debug.log*
yarn-debug.log*

# Temporary folders
tmp/
temp/ 