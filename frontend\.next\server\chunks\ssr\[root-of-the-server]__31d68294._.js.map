{"version": 3, "sources": [], "sections": [{"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/api/errorHandler.js"], "sourcesContent": ["import { useNotification } from '../contexts/NotificationContext';\r\n\r\n// Error codes and their corresponding handlers\r\nexport const ERROR_PATTERNS = {\r\n  AUTHENTICATION: {\r\n    codes: [401],\r\n    keywords: ['unauthorized', 'authentication', 'token', 'login'],\r\n    handler: 'showAuthError'\r\n  },\r\n  SESSION_EXPIRED: {\r\n    codes: [401],\r\n    keywords: ['expired', 'invalid token', 'token expired'],\r\n    handler: 'showSessionExpiredModal'\r\n  },\r\n  PERMISSION_DENIED: {\r\n    codes: [403],\r\n    keywords: ['permission', 'forbidden', 'access denied'],\r\n    handler: 'showAuthError'\r\n  },\r\n  VALIDATION: {\r\n    codes: [400, 422],\r\n    keywords: ['validation', 'invalid', 'required'],\r\n    handler: 'showValidationError'\r\n  },\r\n  RESUME_REQUIRED: {\r\n    fields: ['resume'],\r\n    keywords: ['resume', 'must be uploaded', 'present in the student profile'],\r\n    handler: 'showMissingResumeModal'\r\n  },\r\n  PROFILE_INCOMPLETE: {\r\n    keywords: ['profile incomplete', 'missing profile', 'update profile'],\r\n    handler: 'showProfileIncompleteModal'\r\n  },\r\n  FILE_UPLOAD: {\r\n    keywords: ['file', 'upload', 'size', 'format', 'extension'],\r\n    handler: 'showFileUploadError'\r\n  },\r\n  NETWORK_ERROR: {\r\n    codes: ['NETWORK_ERROR', 'ECONNREFUSED', 'ERR_NETWORK'],\r\n    keywords: ['network', 'connection', 'timeout'],\r\n    handler: 'showNetworkError'\r\n  },\r\n  MAINTENANCE: {\r\n    codes: [503, 502],\r\n    keywords: ['maintenance', 'service unavailable', 'temporarily unavailable'],\r\n    handler: 'showMaintenanceModal'\r\n  }\r\n};\r\n\r\n// Smart error detection and handling\r\nexport const detectAndHandleError = (error, context = '', notificationHandlers) => {\r\n  const errorData = error?.response?.data || {};\r\n  const errorMessage = (errorData.detail || errorData.message || error.message || '').toLowerCase();\r\n  const statusCode = error?.response?.status;\r\n\r\n  // Check for specific error patterns\r\n  for (const [pattern, config] of Object.entries(ERROR_PATTERNS)) {\r\n    // Check status codes\r\n    if (config.codes && config.codes.includes(statusCode)) {\r\n      // Additional keyword check for more precision\r\n      if (config.keywords && !config.keywords.some(keyword => errorMessage.includes(keyword))) {\r\n        continue;\r\n      }\r\n      \r\n      return handleSpecificError(pattern, error, context, notificationHandlers);\r\n    }\r\n\r\n    // Check for field-specific errors (like resume)\r\n    if (config.fields && config.fields.some(field => errorData[field])) {\r\n      return handleSpecificError(pattern, error, context, notificationHandlers);\r\n    }\r\n\r\n    // Check keywords in error message\r\n    if (config.keywords && config.keywords.some(keyword => errorMessage.includes(keyword))) {\r\n      return handleSpecificError(pattern, error, context, notificationHandlers);\r\n    }\r\n  }\r\n\r\n  // Fallback to generic error handling\r\n  return handleGenericError(error, context, notificationHandlers);\r\n};\r\n\r\nconst handleSpecificError = (pattern, error, context, notificationHandlers) => {\r\n  const config = ERROR_PATTERNS[pattern];\r\n  const handlerName = config.handler;\r\n  \r\n  if (notificationHandlers[handlerName]) {\r\n    switch (handlerName) {\r\n      case 'showMissingResumeModal':\r\n        notificationHandlers.showMissingResumeModal();\r\n        break;\r\n      case 'showSessionExpiredModal':\r\n        notificationHandlers.showSessionExpiredModal();\r\n        break;\r\n      case 'showMaintenanceModal':\r\n        notificationHandlers.showMaintenanceModal();\r\n        break;\r\n      case 'showValidationError':\r\n        const errorData = error?.response?.data || {};\r\n        notificationHandlers.showValidationError(\r\n          `Validation Error ${context ? `in ${context}` : ''}`, \r\n          errorData\r\n        );\r\n        break;\r\n      case 'showAuthError':\r\n        const message = error?.response?.data?.detail || \r\n                       error?.response?.data?.message || \r\n                       `Authentication failed${context ? ` while ${context}` : ''}`;\r\n        notificationHandlers.showAuthError(message);\r\n        break;\r\n      case 'showFileUploadError':\r\n        notificationHandlers.showFileUploadError();\r\n        break;\r\n      case 'showNetworkError':\r\n        notificationHandlers.showNetworkError(error);\r\n        break;\r\n      case 'showProfileIncompleteModal':\r\n        notificationHandlers.showProfileIncompleteModal();\r\n        break;\r\n      default:\r\n        return handleGenericError(error, context, notificationHandlers);\r\n    }\r\n    return true; // Error was handled\r\n  }\r\n  \r\n  return false; // Error not handled\r\n};\r\n\r\nconst handleGenericError = (error, context, notificationHandlers) => {\r\n  if (notificationHandlers.handleApiError) {\r\n    notificationHandlers.handleApiError(error, context);\r\n    return true;\r\n  }\r\n  \r\n  // Ultimate fallback\r\n  console.error('Unhandled error:', error);\r\n  return false;\r\n};\r\n\r\n// Hook for easy error handling in components\r\nexport const useErrorHandler = () => {\r\n  const notificationHandlers = useNotification();\r\n  \r\n  const handleError = (error, context = '') => {\r\n    return detectAndHandleError(error, context, notificationHandlers);\r\n  };\r\n\r\n  return { handleError };\r\n};\r\n\r\n// Axios interceptor setup\r\nexport const setupErrorInterceptor = (axiosInstance, notificationHandlers) => {\r\n  axiosInstance.interceptors.response.use(\r\n    (response) => response,\r\n    (error) => {\r\n      // Automatically handle common errors\r\n      detectAndHandleError(error, 'API request', notificationHandlers);\r\n      return Promise.reject(error);\r\n    }\r\n  );\r\n};\r\n\r\nexport default {\r\n  detectAndHandleError,\r\n  useErrorHandler,\r\n  setupErrorInterceptor,\r\n  ERROR_PATTERNS\r\n}; "], "names": [], "mappings": ";;;;;;;AAAA;;AAGO,MAAM,iBAAiB;IAC5B,gBAAgB;QACd,OAAO;YAAC;SAAI;QACZ,UAAU;YAAC;YAAgB;YAAkB;YAAS;SAAQ;QAC9D,SAAS;IACX;IACA,iBAAiB;QACf,OAAO;YAAC;SAAI;QACZ,UAAU;YAAC;YAAW;YAAiB;SAAgB;QACvD,SAAS;IACX;IACA,mBAAmB;QACjB,OAAO;YAAC;SAAI;QACZ,UAAU;YAAC;YAAc;YAAa;SAAgB;QACtD,SAAS;IACX;IACA,YAAY;QACV,OAAO;YAAC;YAAK;SAAI;QACjB,UAAU;YAAC;YAAc;YAAW;SAAW;QAC/C,SAAS;IACX;IACA,iBAAiB;QACf,QAAQ;YAAC;SAAS;QAClB,UAAU;YAAC;YAAU;YAAoB;SAAiC;QAC1E,SAAS;IACX;IACA,oBAAoB;QAClB,UAAU;YAAC;YAAsB;YAAmB;SAAiB;QACrE,SAAS;IACX;IACA,aAAa;QACX,UAAU;YAAC;YAAQ;YAAU;YAAQ;YAAU;SAAY;QAC3D,SAAS;IACX;IACA,eAAe;QACb,OAAO;YAAC;YAAiB;YAAgB;SAAc;QACvD,UAAU;YAAC;YAAW;YAAc;SAAU;QAC9C,SAAS;IACX;IACA,aAAa;QACX,OAAO;YAAC;YAAK;SAAI;QACjB,UAAU;YAAC;YAAe;YAAuB;SAA0B;QAC3E,SAAS;IACX;AACF;AAGO,MAAM,uBAAuB,CAAC,OAAO,UAAU,EAAE,EAAE;IACxD,MAAM,YAAY,OAAO,UAAU,QAAQ,CAAC;IAC5C,MAAM,eAAe,CAAC,UAAU,MAAM,IAAI,UAAU,OAAO,IAAI,MAAM,OAAO,IAAI,EAAE,EAAE,WAAW;IAC/F,MAAM,aAAa,OAAO,UAAU;IAEpC,oCAAoC;IACpC,KAAK,MAAM,CAAC,SAAS,OAAO,IAAI,OAAO,OAAO,CAAC,gBAAiB;QAC9D,qBAAqB;QACrB,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,CAAC,QAAQ,CAAC,aAAa;YACrD,8CAA8C;YAC9C,IAAI,OAAO,QAAQ,IAAI,CAAC,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAA,UAAW,aAAa,QAAQ,CAAC,WAAW;gBACvF;YACF;YAEA,OAAO,oBAAoB,SAAS,OAAO,SAAS;QACtD;QAEA,gDAAgD;QAChD,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,CAAA,QAAS,SAAS,CAAC,MAAM,GAAG;YAClE,OAAO,oBAAoB,SAAS,OAAO,SAAS;QACtD;QAEA,kCAAkC;QAClC,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAA,UAAW,aAAa,QAAQ,CAAC,WAAW;YACtF,OAAO,oBAAoB,SAAS,OAAO,SAAS;QACtD;IACF;IAEA,qCAAqC;IACrC,OAAO,mBAAmB,OAAO,SAAS;AAC5C;AAEA,MAAM,sBAAsB,CAAC,SAAS,OAAO,SAAS;IACpD,MAAM,SAAS,cAAc,CAAC,QAAQ;IACtC,MAAM,cAAc,OAAO,OAAO;IAElC,IAAI,oBAAoB,CAAC,YAAY,EAAE;QACrC,OAAQ;YACN,KAAK;gBACH,qBAAqB,sBAAsB;gBAC3C;YACF,KAAK;gBACH,qBAAqB,uBAAuB;gBAC5C;YACF,KAAK;gBACH,qBAAqB,oBAAoB;gBACzC;YACF,KAAK;gBACH,MAAM,YAAY,OAAO,UAAU,QAAQ,CAAC;gBAC5C,qBAAqB,mBAAmB,CACtC,CAAC,iBAAiB,EAAE,UAAU,CAAC,GAAG,EAAE,SAAS,GAAG,IAAI,EACpD;gBAEF;YACF,KAAK;gBACH,MAAM,UAAU,OAAO,UAAU,MAAM,UACxB,OAAO,UAAU,MAAM,WACvB,CAAC,qBAAqB,EAAE,UAAU,CAAC,OAAO,EAAE,SAAS,GAAG,IAAI;gBAC3E,qBAAqB,aAAa,CAAC;gBACnC;YACF,KAAK;gBACH,qBAAqB,mBAAmB;gBACxC;YACF,KAAK;gBACH,qBAAqB,gBAAgB,CAAC;gBACtC;YACF,KAAK;gBACH,qBAAqB,0BAA0B;gBAC/C;YACF;gBACE,OAAO,mBAAmB,OAAO,SAAS;QAC9C;QACA,OAAO,MAAM,oBAAoB;IACnC;IAEA,OAAO,OAAO,oBAAoB;AACpC;AAEA,MAAM,qBAAqB,CAAC,OAAO,SAAS;IAC1C,IAAI,qBAAqB,cAAc,EAAE;QACvC,qBAAqB,cAAc,CAAC,OAAO;QAC3C,OAAO;IACT;IAEA,oBAAoB;IACpB,QAAQ,KAAK,CAAC,oBAAoB;IAClC,OAAO;AACT;AAGO,MAAM,kBAAkB;IAC7B,MAAM,uBAAuB,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAE3C,MAAM,cAAc,CAAC,OAAO,UAAU,EAAE;QACtC,OAAO,qBAAqB,OAAO,SAAS;IAC9C;IAEA,OAAO;QAAE;IAAY;AACvB;AAGO,MAAM,wBAAwB,CAAC,eAAe;IACnD,cAAc,YAAY,CAAC,QAAQ,CAAC,GAAG,CACrC,CAAC,WAAa,UACd,CAAC;QACC,qCAAqC;QACrC,qBAAqB,OAAO,eAAe;QAC3C,OAAO,QAAQ,MAAM,CAAC;IACxB;AAEJ;uCAEe;IACb;IACA;IACA;IACA;AACF", "debugId": null}}, {"offset": {"line": 323, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/api/client.js"], "sourcesContent": ["import axios from 'axios';\r\nimport { setupErrorInterceptor } from './errorHandler';\r\n\r\nconst client = axios.create({\r\n  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000',\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n});\r\n\r\n// Add a request interceptor to include the auth token\r\nclient.interceptors.request.use(\r\n  (config) => {\r\n    // Get the token from localStorage\r\n    const token = localStorage.getItem('access_token');\r\n    \r\n    // If token exists, add it to the Authorization header\r\n    if (token) {\r\n      config.headers['Authorization'] = `Bearer ${token}`;\r\n    }\r\n    \r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Add a response interceptor to handle 401 errors (token expired)\r\nclient.interceptors.response.use(\r\n  (response) => response,\r\n  async (error) => {\r\n    const originalRequest = error.config;\r\n    \r\n    // If error is 401 and we haven't tried to refresh the token yet\r\n    if (error.response?.status === 401 && !originalRequest._retry) {\r\n      originalRequest._retry = true;\r\n      \r\n      try {\r\n        // Get refresh token\r\n        const refreshToken = localStorage.getItem('refresh_token');\r\n        \r\n        if (refreshToken) {\r\n          // Try to get a new token\r\n          const response = await axios.post('http://127.0.0.1:8000/api/auth/token/refresh/', {\r\n            refresh: refreshToken\r\n          });\r\n          \r\n          // Store the new tokens\r\n          localStorage.setItem('access_token', response.data.access);\r\n          \r\n          // Update the Authorization header\r\n          originalRequest.headers['Authorization'] = `Bearer ${response.data.access}`;\r\n          \r\n          // Retry the original request\r\n          return client(originalRequest);\r\n        }\r\n      } catch (refreshError) {\r\n        console.error('Error refreshing token:', refreshError);\r\n        \r\n        // If token refresh fails, redirect to login\r\n        if (typeof window !== 'undefined') {\r\n          // Clear tokens\r\n          localStorage.removeItem('access_token');\r\n          localStorage.removeItem('refresh_token');\r\n          \r\n          // Redirect to login page\r\n          window.location.href = '/login';\r\n        }\r\n      }\r\n    }\r\n    \r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport default client;\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,SAAS,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC1B,SAAS,6DAAmC;IAC5C,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,sDAAsD;AACtD,OAAO,YAAY,CAAC,OAAO,CAAC,GAAG,CAC7B,CAAC;IACC,kCAAkC;IAClC,MAAM,QAAQ,aAAa,OAAO,CAAC;IAEnC,sDAAsD;IACtD,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;IACrD;IAEA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,kEAAkE;AAClE,OAAO,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC9B,CAAC,WAAa,UACd,OAAO;IACL,MAAM,kBAAkB,MAAM,MAAM;IAEpC,gEAAgE;IAChE,IAAI,MAAM,QAAQ,EAAE,WAAW,OAAO,CAAC,gBAAgB,MAAM,EAAE;QAC7D,gBAAgB,MAAM,GAAG;QAEzB,IAAI;YACF,oBAAoB;YACpB,MAAM,eAAe,aAAa,OAAO,CAAC;YAE1C,IAAI,cAAc;gBAChB,yBAAyB;gBACzB,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,iDAAiD;oBACjF,SAAS;gBACX;gBAEA,uBAAuB;gBACvB,aAAa,OAAO,CAAC,gBAAgB,SAAS,IAAI,CAAC,MAAM;gBAEzD,kCAAkC;gBAClC,gBAAgB,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,SAAS,IAAI,CAAC,MAAM,EAAE;gBAE3E,6BAA6B;gBAC7B,OAAO,OAAO;YAChB;QACF,EAAE,OAAO,cAAc;YACrB,QAAQ,KAAK,CAAC,2BAA2B;YAEzC,4CAA4C;YAC5C,uCAAmC;;YAOnC;QACF;IACF;IAEA,OAAO,QAAQ,MAAM,CAAC;AACxB;uCAGa", "debugId": null}}, {"offset": {"line": 386, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/api/auth.js"], "sourcesContent": ["import client from './client';\r\n\r\n// Register new student\r\nexport function signup(data) {\r\n  return client.post('/api/auth/register/student/', data);\r\n}\r\n\r\n// Login and get tokens\r\nexport function login(data) {\r\n  return client.post('/api/auth/login/', data);\r\n}\r\n\r\n// Upload Resume\r\nexport function uploadResume(file, accessToken) {\r\n  const formData = new FormData();\r\n  formData.append('resume', file);\r\n\r\n  return client.patch('/api/auth/profile/', formData, {\r\n    headers: {\r\n      'Authorization': `Bearer ${accessToken}`,\r\n      'Content-Type': 'multipart/form-data',\r\n    }\r\n  });\r\n}\r\n\r\nexport const getAuthToken = () => {\r\n  if (typeof window !== 'undefined') {\r\n    return localStorage.getItem('access_token');\r\n  }\r\n  return null;\r\n};\r\n\r\nexport const setAuthToken = (token) => {\r\n  if (typeof window !== 'undefined') {\r\n    localStorage.setItem('access_token', token);\r\n  }\r\n};\r\n\r\nexport const removeAuthToken = () => {\r\n  if (typeof window !== 'undefined') {\r\n    localStorage.removeItem('access_token');\r\n    localStorage.removeItem('refresh_token');\r\n  }\r\n};\r\n\r\nexport const getRefreshToken = () => {\r\n  if (typeof window !== 'undefined') {\r\n    return localStorage.getItem('refresh_token');\r\n  }\r\n  return null;\r\n};\r\n\r\nexport const setRefreshToken = (token) => {\r\n  if (typeof window !== 'undefined') {\r\n    localStorage.setItem('refresh_token', token);\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAGO,SAAS,OAAO,IAAI;IACzB,OAAO,oHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,+BAA+B;AACpD;AAGO,SAAS,MAAM,IAAI;IACxB,OAAO,oHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,oBAAoB;AACzC;AAGO,SAAS,aAAa,IAAI,EAAE,WAAW;IAC5C,MAAM,WAAW,IAAI;IACrB,SAAS,MAAM,CAAC,UAAU;IAE1B,OAAO,oHAAA,CAAA,UAAM,CAAC,KAAK,CAAC,sBAAsB,UAAU;QAClD,SAAS;YACP,iBAAiB,CAAC,OAAO,EAAE,aAAa;YACxC,gBAAgB;QAClB;IACF;AACF;AAEO,MAAM,eAAe;IAC1B,uCAAmC;;IAEnC;IACA,OAAO;AACT;AAEO,MAAM,eAAe,CAAC;IAC3B,uCAAmC;;IAEnC;AACF;AAEO,MAAM,kBAAkB;IAC7B,uCAAmC;;IAGnC;AACF;AAEO,MAAM,kBAAkB;IAC7B,uCAAmC;;IAEnC;IACA,OAAO;AACT;AAEO,MAAM,kBAAkB,CAAC;IAC9B,uCAAmC;;IAEnC;AACF", "debugId": null}}, {"offset": {"line": 447, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/api/students.js"], "sourcesContent": ["import axios from 'axios';\r\nimport { getAuthToken } from './auth';\r\n\r\n// Set the base URL for all API requests\r\nconst API_BASE_URL = 'http://localhost:8000';\r\n\r\nconst api = axios.create({\r\n  baseURL: API_BASE_URL,\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n});\r\n\r\n// Add request interceptor to include auth token\r\napi.interceptors.request.use(\r\n  (config) => {\r\n    const token = getAuthToken();\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Add response interceptor for error handling\r\napi.interceptors.response.use(\r\n  (response) => response,\r\n  (error) => {\r\n    if (error.response?.status === 401) {\r\n      // Token expired or invalid\r\n      localStorage.removeItem('access_token');\r\n      localStorage.removeItem('refresh_token');\r\n      window.location.href = '/login';\r\n    }\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport const studentsAPI = {\r\n  // Get all students\r\n  getStudents: async (params = {}) => {\r\n    const response = await api.get('/api/accounts/students/', { params });\r\n    return response.data;\r\n  },\r\n\r\n  // Get students with statistics\r\n  getStudentsWithStats: async (params = {}) => {\r\n    try {\r\n      // First try to get students with built-in statistics\r\n      const response = await api.get('/api/accounts/students/stats/', { params });\r\n      return response.data;\r\n    } catch (error) {\r\n      // Fallback to regular students endpoint\r\n      console.log('Stats endpoint not available, using regular endpoint');\r\n      const response = await api.get('/api/accounts/students/', { params });\r\n      \r\n      // Calculate basic statistics from the response\r\n      const students = response.data.data || response.data;\r\n      if (Array.isArray(students)) {\r\n        const stats = calculateStudentStats(students, params);\r\n        return {\r\n          ...response.data,\r\n          statistics: stats\r\n        };\r\n      }\r\n      \r\n      return response.data;\r\n    }\r\n  },\r\n\r\n  // Get single student\r\n  getStudent: async (id) => {\r\n    const response = await api.get(`/api/accounts/students/${id}/`);\r\n    return response.data;\r\n  },\r\n\r\n  // Update student\r\n  updateStudent: async (id, data) => {\r\n    console.log('updateStudent called with:', { id, data });\r\n\r\n    // Check authentication\r\n    const token = getAuthToken();\r\n    console.log('Auth token available:', !!token);\r\n    if (token) {\r\n      console.log('Token preview:', token.substring(0, 20) + '...');\r\n    }\r\n\r\n    if (!token) {\r\n      throw new Error('Authentication required to update student');\r\n    }\r\n\r\n    // Clean data to ensure proper format\r\n    const cleanedData = { ...data };\r\n    \r\n    // Ensure numeric fields are properly formatted\r\n    ['joining_year', 'passout_year'].forEach(field => {\r\n      if (cleanedData[field] !== null && cleanedData[field] !== undefined) {\r\n        const num = parseInt(cleanedData[field]);\r\n        cleanedData[field] = isNaN(num) ? null : num;\r\n      }\r\n    });\r\n\r\n    // Ensure string fields are properly formatted\r\n    const stringFields = [\r\n      'first_name', 'last_name', 'student_id', 'contact_email', 'phone', 'branch', 'gpa',\r\n      'date_of_birth', 'address', 'city', 'district', 'state', 'pincode', 'country',\r\n      'parent_contact', 'education', 'skills',\r\n      'tenth_cgpa', 'tenth_percentage', 'tenth_board', 'tenth_school', 'tenth_year_of_passing', \r\n      'tenth_location', 'tenth_specialization',\r\n      'twelfth_cgpa', 'twelfth_percentage', 'twelfth_board', 'twelfth_school', 'twelfth_year_of_passing',\r\n      'twelfth_location', 'twelfth_specialization'\r\n    ];\r\n\r\n    stringFields.forEach(field => {\r\n      if (cleanedData[field] !== null && cleanedData[field] !== undefined) {\r\n        cleanedData[field] = String(cleanedData[field]).trim();\r\n      }\r\n    });\r\n\r\n    // Remove undefined values\r\n    Object.keys(cleanedData).forEach(key => {\r\n      if (cleanedData[key] === undefined) {\r\n        delete cleanedData[key];\r\n      }\r\n    });\r\n\r\n    console.log('Cleaned data being sent:', cleanedData);\r\n\r\n    // Try the ViewSet endpoint first (more RESTful)\r\n    try {\r\n      console.log('Trying ViewSet endpoint:', `/api/accounts/profiles/${id}/`);\r\n      const response = await api.patch(`/api/accounts/profiles/${id}/`, cleanedData);\r\n      console.log('ViewSet endpoint success:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('ViewSet endpoint failed:', {\r\n        status: error.response?.status,\r\n        statusText: error.response?.statusText,\r\n        data: error.response?.data,\r\n        headers: error.response?.headers,\r\n        config: error.config\r\n      });\r\n\r\n      // If ViewSet fails, try the fallback endpoint\r\n      try {\r\n        console.log('Trying fallback endpoint:', `/api/accounts/students/${id}/update/`);\r\n        const response = await api.patch(`/api/accounts/students/${id}/update/`, cleanedData);\r\n        console.log('Fallback endpoint success:', response.data);\r\n        return response.data;\r\n      } catch (updateError) {\r\n        console.error('Failed to update student via both endpoints:', {\r\n          viewSetError: {\r\n            status: error.response?.status,\r\n            data: error.response?.data\r\n          },\r\n          updateViewError: {\r\n            status: updateError.response?.status,\r\n            data: updateError.response?.data\r\n          }\r\n        });\r\n\r\n        // Throw the more specific error\r\n        const primaryError = updateError.response?.status === 400 ? updateError : error;\r\n        throw primaryError;\r\n      }\r\n    }\r\n  },\r\n\r\n  // Get current user profile\r\n  getProfile: async () => {\r\n    const token = getAuthToken();\r\n    return api.get('/api/auth/profile/', {\r\n      headers: { Authorization: `Bearer ${token}` },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Update profile information\r\n  updateProfile: async (data) => {\r\n    const token = getAuthToken();\r\n    return api.patch('/api/auth/profile/', data, {\r\n      headers: { Authorization: `Bearer ${token}` },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Upload profile image\r\n  uploadProfileImage: async (file) => {\r\n    const token = getAuthToken();\r\n    const formData = new FormData();\r\n    formData.append('image', file);\r\n\r\n    return api.post('/api/accounts/profiles/me/upload_profile_image/', formData, {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Upload resume using new Resume model\r\n  uploadResume: async (file, name = null, isPrimary = false) => {\r\n    const token = getAuthToken();\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n    if (name) {\r\n      formData.append('name', name);\r\n    }\r\n    formData.append('is_primary', isPrimary);\r\n\r\n    return api.post('/api/accounts/profiles/me/resumes/', formData, {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Admin upload resume for specific student\r\n  adminUploadResume: async (studentId, file, name = null, isPrimary = false) => {\r\n    const token = getAuthToken();\r\n    if (!token) {\r\n      throw new Error('No authentication token found');\r\n    }\r\n\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n    if (name) {\r\n      formData.append('name', name);\r\n    }\r\n    formData.append('is_primary', isPrimary);\r\n\r\n    return api.post(`/api/accounts/profiles/${studentId}/upload_resume/`, formData, {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Admin get resumes for specific student\r\n  adminGetResumes: async (studentId) => {\r\n    const token = getAuthToken();\r\n    if (!token) {\r\n      throw new Error('No authentication token found');\r\n    }\r\n\r\n    return api.get(`/api/accounts/profiles/${studentId}/resumes/`, {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n      },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Admin upload certificate for specific student\r\n  adminUploadCertificate: async (studentId, file, type) => {\r\n    const token = getAuthToken();\r\n    if (!token) {\r\n      throw new Error('No authentication token found');\r\n    }\r\n\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n    formData.append('type', type);\r\n\r\n    return api.post(`/api/accounts/profiles/${studentId}/upload_certificate/`, formData, {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Admin upload semester marksheet for specific student\r\n  adminUploadSemesterMarksheet: async (studentId, file, semester, cgpa) => {\r\n    const token = getAuthToken();\r\n    if (!token) {\r\n      throw new Error('No authentication token found');\r\n    }\r\n\r\n    const formData = new FormData();\r\n    formData.append('marksheet_file', file);\r\n    formData.append('semester', semester);\r\n    formData.append('cgpa', cgpa);\r\n\r\n    return api.post(`/api/accounts/profiles/${studentId}/upload_semester_marksheet/`, formData, {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Legacy resume upload (for backward compatibility)\r\n  uploadResumeToProfile: async (file) => {\r\n    const token = getAuthToken();\r\n    const formData = new FormData();\r\n    formData.append('resume', file);\r\n\r\n    return api.patch('/api/auth/profile/', formData, {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Get all resumes for the student\r\n  getResumes: async () => {\r\n    try {\r\n      const token = getAuthToken();\r\n      if (!token) {\r\n        console.log('No authentication token, returning empty array');\r\n        return [];\r\n      }\r\n\r\n      // Try the new resume endpoint first\r\n      const response = await api.get('/api/accounts/profiles/me/resumes/', {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n\r\n      // Ensure we're getting a proper response\r\n      if (!response.data) {\r\n        return await studentsAPI.getResumesLegacy();\r\n      }\r\n\r\n      // Handle different response formats\r\n      if (Array.isArray(response.data)) {\r\n        return response.data;\r\n      } else if (response.data && response.data.data && Array.isArray(response.data.data)) {\r\n        return response.data.data;\r\n      } else {\r\n        console.log('Response data is not an array, trying fallback. Response:', response.data);\r\n        try {\r\n          return await studentsAPI.getResumesLegacy();\r\n        } catch (fallbackError) {\r\n          console.log('Fallback also failed, returning empty array');\r\n          return [];\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.log('Resume endpoint failed, using fallback method');\r\n      try {\r\n        return await studentsAPI.getResumesLegacy();\r\n      } catch (fallbackError) {\r\n        console.log('Fallback method also failed, returning empty array');\r\n        return [];\r\n      }\r\n    }\r\n  },\r\n\r\n  // Legacy method to get resumes from profile\r\n  getResumesLegacy: async () => {\r\n    try {\r\n      const token = getAuthToken();\r\n      if (!token) {\r\n        console.log('No auth token for legacy resume fetch');\r\n        return [];\r\n      }\r\n\r\n      const profile = await studentsAPI.getProfile();\r\n\r\n      if (profile?.resume || profile?.resume_url) {\r\n        const resumeUrl = profile.resume_url || profile.resume;\r\n        if (resumeUrl && resumeUrl.trim() !== '' && resumeUrl !== 'null' && resumeUrl !== 'undefined') {\r\n          const fileName = resumeUrl.split('/').pop() || 'Resume.pdf';\r\n          return [{\r\n            id: profile.id || 1,\r\n            name: fileName,\r\n            file_url: resumeUrl,\r\n            uploaded_at: profile.updated_at || new Date().toISOString()\r\n          }];\r\n        }\r\n      }\r\n      return [];\r\n    } catch (error) {\r\n      console.log('Legacy resume fetch error:', error.message);\r\n      return [];\r\n    }\r\n  },\r\n\r\n  // Delete a specific resume\r\n  deleteResume: async (resumeId) => {\r\n    const token = getAuthToken();\r\n    try {\r\n      console.log(`Attempting to delete resume with ID: ${resumeId}`);\r\n\r\n      // Use the new Resume model endpoint\r\n      const response = await api.delete(`/api/accounts/profiles/me/resumes/${resumeId}/`, {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n      console.log('DELETE resume successful:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error deleting resume:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Legacy delete function with fallback strategies\r\n  deleteResumeLegacy: async (resumeId) => {\r\n    const token = getAuthToken();\r\n    try {\r\n      console.log(`Attempting to delete resume with ID: ${resumeId}`);\r\n\r\n      let success = false;\r\n\r\n      // Attempt different deletion strategies\r\n      const strategies = [\r\n        // Strategy 1: Standard DELETE request\r\n        async () => {\r\n          try {\r\n            const response = await api.delete(`/api/accounts/profiles/me/resumes/${resumeId}/`, {\r\n              headers: { Authorization: `Bearer ${token}` },\r\n            });\r\n            console.log('DELETE resume successful:', response.data);\r\n            return { success: true, data: response.data };\r\n          } catch (error) {\r\n            console.log(`Strategy 1 failed: ${error.message}`);\r\n            return { success: false };\r\n          }\r\n        },\r\n        \r\n        // Strategy 2: POST to remove endpoint\r\n        async () => {\r\n          try {\r\n            const response = await api.post(`/api/accounts/profiles/me/resumes/${resumeId}/remove/`, {}, {\r\n              headers: { Authorization: `Bearer ${token}` },\r\n            });\r\n            console.log('POST remove successful:', response.data);\r\n            return { success: true, data: response.data };\r\n          } catch (error) {\r\n            console.log(`Strategy 2 failed: ${error.message}`);\r\n            return { success: false };\r\n          }\r\n        },\r\n        \r\n        // Strategy 3: Patch profile with delete_resume field\r\n        async () => {\r\n          try {\r\n            const response = await api.patch('/api/auth/profile/', {\r\n              delete_resume: resumeId\r\n            }, {\r\n              headers: { Authorization: `Bearer ${token}` },\r\n            });\r\n            console.log('PATCH profile successful:', response.data);\r\n            return { success: true, data: response.data };\r\n          } catch (error) {\r\n            console.log(`Strategy 3 failed: ${error.message}`);\r\n            return { success: false };\r\n          }\r\n        },\r\n        \r\n        // Strategy 4: Reset all resumes (extreme fallback)\r\n        async () => {\r\n          try {\r\n            const response = await api.patch('/api/auth/profile/', {\r\n              reset_resumes: true\r\n            }, {\r\n              headers: { Authorization: `Bearer ${token}` },\r\n            });\r\n            console.log('Reset resumes successful:', response.data);\r\n            return { success: true, data: response.data };\r\n          } catch (error) {\r\n            console.log(`Strategy 4 failed: ${error.message}`);\r\n            return { success: false };\r\n          }\r\n        }\r\n      ];\r\n      \r\n      // Try each strategy in sequence until one succeeds\r\n      for (const strategy of strategies) {\r\n        const result = await strategy();\r\n        if (result.success) {\r\n          success = true;\r\n          break;\r\n        }\r\n      }\r\n      \r\n      // Clear any locally cached data for this resume regardless of backend success\r\n      if (typeof window !== 'undefined') {\r\n        // Clear any resume-related data from localStorage\r\n        try {\r\n          const localStorageKeys = Object.keys(localStorage);\r\n          const resumeKeys = localStorageKeys.filter(key => \r\n            key.includes('resume') || key.includes('file') || key.includes('document')\r\n          );\r\n          \r\n          if (resumeKeys.length > 0) {\r\n            console.log('Clearing resume-related localStorage items:', resumeKeys);\r\n            resumeKeys.forEach(key => localStorage.removeItem(key));\r\n          }\r\n          \r\n          // Also try to clear specific keys that might be used for caching\r\n          localStorage.removeItem('resume_cache');\r\n          localStorage.removeItem('resume_list');\r\n          localStorage.removeItem('profile_cache');\r\n          localStorage.removeItem('resume_count');\r\n          localStorage.removeItem('last_resume_update');\r\n        } catch (e) {\r\n          console.error('Error clearing localStorage:', e);\r\n        }\r\n      }\r\n      \r\n      return { success, message: success ? \"Resume deleted successfully\" : \"Resume deleted locally but server sync failed\" };\r\n    } catch (error) {\r\n      console.error('Resume deletion failed:', error.response?.status, error.message);\r\n      // For UI purposes, return a success response even if backend fails\r\n      // This allows the UI to remove the resume entry and maintain a good user experience\r\n      return { \r\n        success: true,  // Return true for UI purposes\r\n        synced: false,  // But indicate sync status\r\n        error: error.message,\r\n        status: error.response?.status,\r\n        message: \"Resume removed from display (sync with server failed)\"\r\n      };\r\n    }\r\n  },\r\n\r\n  // Upload certificate (10th or 12th)\r\n  uploadCertificate: async (file, type) => {\r\n    const token = getAuthToken();\r\n    const formData = new FormData();\r\n    formData.append('file', file);  // Backend expects 'file', not 'certificate'\r\n    formData.append('type', type);\r\n\r\n    return api.post('/api/accounts/profiles/me/upload_certificate/', formData, {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Get all certificates for the student\r\n  getCertificates: async () => {\r\n    const token = getAuthToken();\r\n    if (!token) {\r\n      throw new Error('Authentication required to fetch certificates');\r\n    }\r\n    \r\n    try {\r\n      const response = await api.get('/api/accounts/profiles/me/certificates/', {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n      \r\n      // Ensure we're getting a proper response\r\n      if (!response.data) {\r\n        console.error('Empty response when fetching certificates');\r\n        return [];\r\n      }\r\n      \r\n      // Handle different response formats\r\n      if (Array.isArray(response.data)) {\r\n        return response.data;\r\n      } else if (response.data.data && Array.isArray(response.data.data)) {\r\n        return response.data.data;\r\n      } else {\r\n        console.error('Unexpected certificate data format:', response.data);\r\n        return [];\r\n      }\r\n    } catch (error) {\r\n      console.error('Certificate fetch error:', error.response?.status, error.message);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Delete a specific certificate\r\n  deleteCertificate: async (certificateType) => {\r\n    const token = getAuthToken();\r\n    try {\r\n      console.log(`Attempting to delete certificate: ${certificateType}`);\r\n\r\n      const response = await api.delete(`/api/accounts/profiles/me/delete_certificate/${certificateType}/`, {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n      console.log('DELETE certificate successful:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error deleting certificate:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Admin delete certificate for specific student\r\n  adminDeleteCertificate: async (studentId, certificateType) => {\r\n    const token = getAuthToken();\r\n    try {\r\n      console.log(`Admin attempting to delete certificate: ${certificateType} for student: ${studentId}`);\r\n\r\n      const response = await api.delete(`/api/accounts/profiles/${studentId}/delete_certificate/${certificateType}/`, {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n      console.log('Admin DELETE certificate successful:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error deleting certificate:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Delete a specific marksheet\r\n  deleteMarksheet: async (semester) => {\r\n    const token = getAuthToken();\r\n    try {\r\n      console.log(`Attempting to delete marksheet for semester: ${semester}`);\r\n\r\n      const response = await api.delete(`/api/accounts/profiles/me/delete_marksheet/${semester}/`, {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n      console.log('DELETE marksheet successful:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error deleting marksheet:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Admin delete marksheet for specific student\r\n  adminDeleteMarksheet: async (studentId, semester) => {\r\n    const token = getAuthToken();\r\n    try {\r\n      console.log(`Admin attempting to delete marksheet for semester: ${semester} for student: ${studentId}`);\r\n\r\n      const response = await api.delete(`/api/accounts/profiles/${studentId}/delete_marksheet/${semester}/`, {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      });\r\n      console.log('Admin DELETE marksheet successful:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error deleting marksheet:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Legacy delete function (keeping for backward compatibility)\r\n  deleteCertificateLegacy: async (certificateId) => {\r\n    const token = getAuthToken();\r\n    try {\r\n      console.log(`Attempting to delete certificate with ID: ${certificateId}`);\r\n\r\n      let success = false;\r\n\r\n      // Attempt different deletion strategies\r\n      const strategies = [\r\n        // Strategy 1: Standard DELETE request\r\n        async () => {\r\n          try {\r\n            const response = await api.delete(`/api/accounts/profiles/me/certificates/${certificateId}/`, {\r\n              headers: { Authorization: `Bearer ${token}` },\r\n            });\r\n            console.log('DELETE certificate successful:', response.data);\r\n            return { success: true, data: response.data };\r\n          } catch (error) {\r\n            console.log(`Strategy 1 failed: ${error.message}`);\r\n            return { success: false };\r\n          }\r\n        },\r\n\r\n        // Strategy 2: POST to remove endpoint\r\n        async () => {\r\n          try {\r\n            const response = await api.post(`/api/accounts/profiles/me/certificates/${certificateId}/remove/`, {}, {\r\n              headers: { Authorization: `Bearer ${token}` },\r\n            });\r\n            console.log('POST remove successful:', response.data);\r\n            return { success: true, data: response.data };\r\n          } catch (error) {\r\n            console.log(`Strategy 2 failed: ${error.message}`);\r\n            return { success: false };\r\n          }\r\n        },\r\n        \r\n        // Strategy 3: Patch profile with delete_certificate field\r\n        async () => {\r\n          try {\r\n            const response = await api.patch('/api/auth/profile/', {\r\n              delete_certificate: certificateId\r\n            }, {\r\n              headers: { Authorization: `Bearer ${token}` },\r\n            });\r\n            console.log('PATCH profile successful:', response.data);\r\n            return { success: true, data: response.data };\r\n          } catch (error) {\r\n            console.log(`Strategy 3 failed: ${error.message}`);\r\n            return { success: false };\r\n          }\r\n        },\r\n        \r\n        // Strategy 4: Reset all certificates (extreme fallback)\r\n        async () => {\r\n          try {\r\n            const response = await api.patch('/api/auth/profile/', {\r\n              reset_certificates: true\r\n            }, {\r\n              headers: { Authorization: `Bearer ${token}` },\r\n            });\r\n            console.log('Reset certificates successful:', response.data);\r\n            return { success: true, data: response.data };\r\n          } catch (error) {\r\n            console.log(`Strategy 4 failed: ${error.message}`);\r\n            return { success: false };\r\n          }\r\n        }\r\n      ];\r\n      \r\n      // Try each strategy in sequence until one succeeds\r\n      for (const strategy of strategies) {\r\n        const result = await strategy();\r\n        if (result.success) {\r\n          success = true;\r\n          break;\r\n        }\r\n      }\r\n      \r\n      // Clear any locally cached data for this certificate regardless of backend success\r\n      if (typeof window !== 'undefined') {\r\n        // Clear any certificate-related data from localStorage\r\n        try {\r\n          const localStorageKeys = Object.keys(localStorage);\r\n          const certificateKeys = localStorageKeys.filter(key => \r\n            key.includes('certificate') || key.includes('document') || key.includes('cert')\r\n          );\r\n          \r\n          if (certificateKeys.length > 0) {\r\n            console.log('Clearing certificate-related localStorage items:', certificateKeys);\r\n            certificateKeys.forEach(key => localStorage.removeItem(key));\r\n          }\r\n          \r\n          // Also try to clear specific keys that might be used for caching\r\n          localStorage.removeItem('certificate_cache');\r\n          localStorage.removeItem('certificate_list');\r\n          localStorage.removeItem('profile_cache');\r\n        } catch (e) {\r\n          console.error('Error clearing localStorage:', e);\r\n        }\r\n      }\r\n      \r\n      return { success, message: success ? \"Certificate deleted successfully\" : \"Certificate deleted locally but server sync failed\" };\r\n    } catch (error) {\r\n      console.error('Certificate deletion failed:', error.response?.status, error.message);\r\n      // For UI purposes, return a success response even if backend fails\r\n      // This allows the UI to remove the certificate entry and maintain a good user experience\r\n      return { \r\n        success: true,  // Return true for UI purposes\r\n        synced: false,  // But indicate sync status\r\n        error: error.message,\r\n        status: error.response?.status,\r\n        message: \"Certificate removed from display (sync with server failed)\"\r\n      };\r\n    }\r\n  },\r\n\r\n  // Get semester marksheets\r\n  getSemesterMarksheets: async () => {\r\n    const token = getAuthToken();\r\n    return api.get('/api/accounts/profiles/me/semester_marksheets/', {\r\n      headers: { Authorization: `Bearer ${token}` },\r\n    }).then((response) => response.data);\r\n  },\r\n\r\n  // Upload semester marksheet\r\n  uploadSemesterMarksheet: async (file, semester, cgpa) => {\r\n    const token = getAuthToken();\r\n    const formData = new FormData();\r\n    formData.append('marksheet_file', file);\r\n    formData.append('semester', semester);\r\n    formData.append('cgpa', cgpa);\r\n\r\n    return api.post('/api/accounts/profiles/me/upload_semester_marksheet/', formData, {\r\n      headers: {\r\n        Authorization: `Bearer ${token}`,\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    }).then((response) => response.data);\r\n  },\r\n};\r\n\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,wCAAwC;AACxC,MAAM,eAAe;AAErB,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,gDAAgD;AAChD,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,CAC1B,CAAC;IACC,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;IACzB,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,8CAA8C;AAC9C,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC3B,CAAC,WAAa,UACd,CAAC;IACC,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;QAClC,2BAA2B;QAC3B,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGK,MAAM,cAAc;IACzB,mBAAmB;IACnB,aAAa,OAAO,SAAS,CAAC,CAAC;QAC7B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,2BAA2B;YAAE;QAAO;QACnE,OAAO,SAAS,IAAI;IACtB;IAEA,+BAA+B;IAC/B,sBAAsB,OAAO,SAAS,CAAC,CAAC;QACtC,IAAI;YACF,qDAAqD;YACrD,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,iCAAiC;gBAAE;YAAO;YACzE,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,wCAAwC;YACxC,QAAQ,GAAG,CAAC;YACZ,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,2BAA2B;gBAAE;YAAO;YAEnE,+CAA+C;YAC/C,MAAM,WAAW,SAAS,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI;YACpD,IAAI,MAAM,OAAO,CAAC,WAAW;gBAC3B,MAAM,QAAQ,sBAAsB,UAAU;gBAC9C,OAAO;oBACL,GAAG,SAAS,IAAI;oBAChB,YAAY;gBACd;YACF;YAEA,OAAO,SAAS,IAAI;QACtB;IACF;IAEA,qBAAqB;IACrB,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,iBAAiB;IACjB,eAAe,OAAO,IAAI;QACxB,QAAQ,GAAG,CAAC,8BAA8B;YAAE;YAAI;QAAK;QAErD,uBAAuB;QACvB,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,QAAQ,GAAG,CAAC,yBAAyB,CAAC,CAAC;QACvC,IAAI,OAAO;YACT,QAAQ,GAAG,CAAC,kBAAkB,MAAM,SAAS,CAAC,GAAG,MAAM;QACzD;QAEA,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,qCAAqC;QACrC,MAAM,cAAc;YAAE,GAAG,IAAI;QAAC;QAE9B,+CAA+C;QAC/C;YAAC;YAAgB;SAAe,CAAC,OAAO,CAAC,CAAA;YACvC,IAAI,WAAW,CAAC,MAAM,KAAK,QAAQ,WAAW,CAAC,MAAM,KAAK,WAAW;gBACnE,MAAM,MAAM,SAAS,WAAW,CAAC,MAAM;gBACvC,WAAW,CAAC,MAAM,GAAG,MAAM,OAAO,OAAO;YAC3C;QACF;QAEA,8CAA8C;QAC9C,MAAM,eAAe;YACnB;YAAc;YAAa;YAAc;YAAiB;YAAS;YAAU;YAC7E;YAAiB;YAAW;YAAQ;YAAY;YAAS;YAAW;YACpE;YAAkB;YAAa;YAC/B;YAAc;YAAoB;YAAe;YAAgB;YACjE;YAAkB;YAClB;YAAgB;YAAsB;YAAiB;YAAkB;YACzE;YAAoB;SACrB;QAED,aAAa,OAAO,CAAC,CAAA;YACnB,IAAI,WAAW,CAAC,MAAM,KAAK,QAAQ,WAAW,CAAC,MAAM,KAAK,WAAW;gBACnE,WAAW,CAAC,MAAM,GAAG,OAAO,WAAW,CAAC,MAAM,EAAE,IAAI;YACtD;QACF;QAEA,0BAA0B;QAC1B,OAAO,IAAI,CAAC,aAAa,OAAO,CAAC,CAAA;YAC/B,IAAI,WAAW,CAAC,IAAI,KAAK,WAAW;gBAClC,OAAO,WAAW,CAAC,IAAI;YACzB;QACF;QAEA,QAAQ,GAAG,CAAC,4BAA4B;QAExC,gDAAgD;QAChD,IAAI;YACF,QAAQ,GAAG,CAAC,4BAA4B,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC;YACvE,MAAM,WAAW,MAAM,IAAI,KAAK,CAAC,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC,EAAE;YAClE,QAAQ,GAAG,CAAC,6BAA6B,SAAS,IAAI;YACtD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;gBACxC,QAAQ,MAAM,QAAQ,EAAE;gBACxB,YAAY,MAAM,QAAQ,EAAE;gBAC5B,MAAM,MAAM,QAAQ,EAAE;gBACtB,SAAS,MAAM,QAAQ,EAAE;gBACzB,QAAQ,MAAM,MAAM;YACtB;YAEA,8CAA8C;YAC9C,IAAI;gBACF,QAAQ,GAAG,CAAC,6BAA6B,CAAC,uBAAuB,EAAE,GAAG,QAAQ,CAAC;gBAC/E,MAAM,WAAW,MAAM,IAAI,KAAK,CAAC,CAAC,uBAAuB,EAAE,GAAG,QAAQ,CAAC,EAAE;gBACzE,QAAQ,GAAG,CAAC,8BAA8B,SAAS,IAAI;gBACvD,OAAO,SAAS,IAAI;YACtB,EAAE,OAAO,aAAa;gBACpB,QAAQ,KAAK,CAAC,gDAAgD;oBAC5D,cAAc;wBACZ,QAAQ,MAAM,QAAQ,EAAE;wBACxB,MAAM,MAAM,QAAQ,EAAE;oBACxB;oBACA,iBAAiB;wBACf,QAAQ,YAAY,QAAQ,EAAE;wBAC9B,MAAM,YAAY,QAAQ,EAAE;oBAC9B;gBACF;gBAEA,gCAAgC;gBAChC,MAAM,eAAe,YAAY,QAAQ,EAAE,WAAW,MAAM,cAAc;gBAC1E,MAAM;YACR;QACF;IACF;IAEA,2BAA2B;IAC3B,YAAY;QACV,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,OAAO,IAAI,GAAG,CAAC,sBAAsB;YACnC,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAC9C,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,6BAA6B;IAC7B,eAAe,OAAO;QACpB,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,OAAO,IAAI,KAAK,CAAC,sBAAsB,MAAM;YAC3C,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAC9C,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,uBAAuB;IACvB,oBAAoB,OAAO;QACzB,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS;QAEzB,OAAO,IAAI,IAAI,CAAC,mDAAmD,UAAU;YAC3E,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,gBAAgB;YAClB;QACF,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,uCAAuC;IACvC,cAAc,OAAO,MAAM,OAAO,IAAI,EAAE,YAAY,KAAK;QACvD,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QACxB,IAAI,MAAM;YACR,SAAS,MAAM,CAAC,QAAQ;QAC1B;QACA,SAAS,MAAM,CAAC,cAAc;QAE9B,OAAO,IAAI,IAAI,CAAC,sCAAsC,UAAU;YAC9D,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,gBAAgB;YAClB;QACF,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,2CAA2C;IAC3C,mBAAmB,OAAO,WAAW,MAAM,OAAO,IAAI,EAAE,YAAY,KAAK;QACvE,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QACxB,IAAI,MAAM;YACR,SAAS,MAAM,CAAC,QAAQ;QAC1B;QACA,SAAS,MAAM,CAAC,cAAc;QAE9B,OAAO,IAAI,IAAI,CAAC,CAAC,uBAAuB,EAAE,UAAU,eAAe,CAAC,EAAE,UAAU;YAC9E,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,gBAAgB;YAClB;QACF,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,yCAAyC;IACzC,iBAAiB,OAAO;QACtB,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,IAAI,GAAG,CAAC,CAAC,uBAAuB,EAAE,UAAU,SAAS,CAAC,EAAE;YAC7D,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;YAClC;QACF,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,gDAAgD;IAChD,wBAAwB,OAAO,WAAW,MAAM;QAC9C,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QACxB,SAAS,MAAM,CAAC,QAAQ;QAExB,OAAO,IAAI,IAAI,CAAC,CAAC,uBAAuB,EAAE,UAAU,oBAAoB,CAAC,EAAE,UAAU;YACnF,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,gBAAgB;YAClB;QACF,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,uDAAuD;IACvD,8BAA8B,OAAO,WAAW,MAAM,UAAU;QAC9D,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,kBAAkB;QAClC,SAAS,MAAM,CAAC,YAAY;QAC5B,SAAS,MAAM,CAAC,QAAQ;QAExB,OAAO,IAAI,IAAI,CAAC,CAAC,uBAAuB,EAAE,UAAU,2BAA2B,CAAC,EAAE,UAAU;YAC1F,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,gBAAgB;YAClB;QACF,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,oDAAoD;IACpD,uBAAuB,OAAO;QAC5B,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,UAAU;QAE1B,OAAO,IAAI,KAAK,CAAC,sBAAsB,UAAU;YAC/C,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,gBAAgB;YAClB;QACF,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,kCAAkC;IAClC,YAAY;QACV,IAAI;YACF,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;YACzB,IAAI,CAAC,OAAO;gBACV,QAAQ,GAAG,CAAC;gBACZ,OAAO,EAAE;YACX;YAEA,oCAAoC;YACpC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,sCAAsC;gBACnE,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YAEA,yCAAyC;YACzC,IAAI,CAAC,SAAS,IAAI,EAAE;gBAClB,OAAO,MAAM,YAAY,gBAAgB;YAC3C;YAEA,oCAAoC;YACpC,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBAChC,OAAO,SAAS,IAAI;YACtB,OAAO,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,GAAG;gBACnF,OAAO,SAAS,IAAI,CAAC,IAAI;YAC3B,OAAO;gBACL,QAAQ,GAAG,CAAC,6DAA6D,SAAS,IAAI;gBACtF,IAAI;oBACF,OAAO,MAAM,YAAY,gBAAgB;gBAC3C,EAAE,OAAO,eAAe;oBACtB,QAAQ,GAAG,CAAC;oBACZ,OAAO,EAAE;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC;YACZ,IAAI;gBACF,OAAO,MAAM,YAAY,gBAAgB;YAC3C,EAAE,OAAO,eAAe;gBACtB,QAAQ,GAAG,CAAC;gBACZ,OAAO,EAAE;YACX;QACF;IACF;IAEA,4CAA4C;IAC5C,kBAAkB;QAChB,IAAI;YACF,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;YACzB,IAAI,CAAC,OAAO;gBACV,QAAQ,GAAG,CAAC;gBACZ,OAAO,EAAE;YACX;YAEA,MAAM,UAAU,MAAM,YAAY,UAAU;YAE5C,IAAI,SAAS,UAAU,SAAS,YAAY;gBAC1C,MAAM,YAAY,QAAQ,UAAU,IAAI,QAAQ,MAAM;gBACtD,IAAI,aAAa,UAAU,IAAI,OAAO,MAAM,cAAc,UAAU,cAAc,aAAa;oBAC7F,MAAM,WAAW,UAAU,KAAK,CAAC,KAAK,GAAG,MAAM;oBAC/C,OAAO;wBAAC;4BACN,IAAI,QAAQ,EAAE,IAAI;4BAClB,MAAM;4BACN,UAAU;4BACV,aAAa,QAAQ,UAAU,IAAI,IAAI,OAAO,WAAW;wBAC3D;qBAAE;gBACJ;YACF;YACA,OAAO,EAAE;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,8BAA8B,MAAM,OAAO;YACvD,OAAO,EAAE;QACX;IACF;IAEA,2BAA2B;IAC3B,cAAc,OAAO;QACnB,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,UAAU;YAE9D,oCAAoC;YACpC,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,kCAAkC,EAAE,SAAS,CAAC,CAAC,EAAE;gBAClF,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YACA,QAAQ,GAAG,CAAC,6BAA6B,SAAS,IAAI;YACtD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA,kDAAkD;IAClD,oBAAoB,OAAO;QACzB,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,UAAU;YAE9D,IAAI,UAAU;YAEd,wCAAwC;YACxC,MAAM,aAAa;gBACjB,sCAAsC;gBACtC;oBACE,IAAI;wBACF,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,kCAAkC,EAAE,SAAS,CAAC,CAAC,EAAE;4BAClF,SAAS;gCAAE,eAAe,CAAC,OAAO,EAAE,OAAO;4BAAC;wBAC9C;wBACA,QAAQ,GAAG,CAAC,6BAA6B,SAAS,IAAI;wBACtD,OAAO;4BAAE,SAAS;4BAAM,MAAM,SAAS,IAAI;wBAAC;oBAC9C,EAAE,OAAO,OAAO;wBACd,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,MAAM,OAAO,EAAE;wBACjD,OAAO;4BAAE,SAAS;wBAAM;oBAC1B;gBACF;gBAEA,sCAAsC;gBACtC;oBACE,IAAI;wBACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,kCAAkC,EAAE,SAAS,QAAQ,CAAC,EAAE,CAAC,GAAG;4BAC3F,SAAS;gCAAE,eAAe,CAAC,OAAO,EAAE,OAAO;4BAAC;wBAC9C;wBACA,QAAQ,GAAG,CAAC,2BAA2B,SAAS,IAAI;wBACpD,OAAO;4BAAE,SAAS;4BAAM,MAAM,SAAS,IAAI;wBAAC;oBAC9C,EAAE,OAAO,OAAO;wBACd,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,MAAM,OAAO,EAAE;wBACjD,OAAO;4BAAE,SAAS;wBAAM;oBAC1B;gBACF;gBAEA,qDAAqD;gBACrD;oBACE,IAAI;wBACF,MAAM,WAAW,MAAM,IAAI,KAAK,CAAC,sBAAsB;4BACrD,eAAe;wBACjB,GAAG;4BACD,SAAS;gCAAE,eAAe,CAAC,OAAO,EAAE,OAAO;4BAAC;wBAC9C;wBACA,QAAQ,GAAG,CAAC,6BAA6B,SAAS,IAAI;wBACtD,OAAO;4BAAE,SAAS;4BAAM,MAAM,SAAS,IAAI;wBAAC;oBAC9C,EAAE,OAAO,OAAO;wBACd,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,MAAM,OAAO,EAAE;wBACjD,OAAO;4BAAE,SAAS;wBAAM;oBAC1B;gBACF;gBAEA,mDAAmD;gBACnD;oBACE,IAAI;wBACF,MAAM,WAAW,MAAM,IAAI,KAAK,CAAC,sBAAsB;4BACrD,eAAe;wBACjB,GAAG;4BACD,SAAS;gCAAE,eAAe,CAAC,OAAO,EAAE,OAAO;4BAAC;wBAC9C;wBACA,QAAQ,GAAG,CAAC,6BAA6B,SAAS,IAAI;wBACtD,OAAO;4BAAE,SAAS;4BAAM,MAAM,SAAS,IAAI;wBAAC;oBAC9C,EAAE,OAAO,OAAO;wBACd,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,MAAM,OAAO,EAAE;wBACjD,OAAO;4BAAE,SAAS;wBAAM;oBAC1B;gBACF;aACD;YAED,mDAAmD;YACnD,KAAK,MAAM,YAAY,WAAY;gBACjC,MAAM,SAAS,MAAM;gBACrB,IAAI,OAAO,OAAO,EAAE;oBAClB,UAAU;oBACV;gBACF;YACF;YAEA,8EAA8E;YAC9E,uCAAmC;;YAsBnC;YAEA,OAAO;gBAAE;gBAAS,SAAS,UAAU,gCAAgC;YAAgD;QACvH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;YAC9E,mEAAmE;YACnE,oFAAoF;YACpF,OAAO;gBACL,SAAS;gBACT,QAAQ;gBACR,OAAO,MAAM,OAAO;gBACpB,QAAQ,MAAM,QAAQ,EAAE;gBACxB,SAAS;YACX;QACF;IACF;IAEA,oCAAoC;IACpC,mBAAmB,OAAO,MAAM;QAC9B,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ,OAAQ,4CAA4C;QAC5E,SAAS,MAAM,CAAC,QAAQ;QAExB,OAAO,IAAI,IAAI,CAAC,iDAAiD,UAAU;YACzE,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,gBAAgB;YAClB;QACF,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,uCAAuC;IACvC,iBAAiB;QACf,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,2CAA2C;gBACxE,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YAEA,yCAAyC;YACzC,IAAI,CAAC,SAAS,IAAI,EAAE;gBAClB,QAAQ,KAAK,CAAC;gBACd,OAAO,EAAE;YACX;YAEA,oCAAoC;YACpC,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBAChC,OAAO,SAAS,IAAI;YACtB,OAAO,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,GAAG;gBAClE,OAAO,SAAS,IAAI,CAAC,IAAI;YAC3B,OAAO;gBACL,QAAQ,KAAK,CAAC,uCAAuC,SAAS,IAAI;gBAClE,OAAO,EAAE;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;YAC/E,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,mBAAmB,OAAO;QACxB,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,kCAAkC,EAAE,iBAAiB;YAElE,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,6CAA6C,EAAE,gBAAgB,CAAC,CAAC,EAAE;gBACpG,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YACA,QAAQ,GAAG,CAAC,kCAAkC,SAAS,IAAI;YAC3D,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACR;IACF;IAEA,gDAAgD;IAChD,wBAAwB,OAAO,WAAW;QACxC,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,gBAAgB,cAAc,EAAE,WAAW;YAElG,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,uBAAuB,EAAE,UAAU,oBAAoB,EAAE,gBAAgB,CAAC,CAAC,EAAE;gBAC9G,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YACA,QAAQ,GAAG,CAAC,wCAAwC,SAAS,IAAI;YACjE,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACR;IACF;IAEA,8BAA8B;IAC9B,iBAAiB,OAAO;QACtB,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,6CAA6C,EAAE,UAAU;YAEtE,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,2CAA2C,EAAE,SAAS,CAAC,CAAC,EAAE;gBAC3F,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YACA,QAAQ,GAAG,CAAC,gCAAgC,SAAS,IAAI;YACzD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,8CAA8C;IAC9C,sBAAsB,OAAO,WAAW;QACtC,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,mDAAmD,EAAE,SAAS,cAAc,EAAE,WAAW;YAEtG,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,uBAAuB,EAAE,UAAU,kBAAkB,EAAE,SAAS,CAAC,CAAC,EAAE;gBACrG,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C;YACA,QAAQ,GAAG,CAAC,sCAAsC,SAAS,IAAI;YAC/D,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,8DAA8D;IAC9D,yBAAyB,OAAO;QAC9B,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,0CAA0C,EAAE,eAAe;YAExE,IAAI,UAAU;YAEd,wCAAwC;YACxC,MAAM,aAAa;gBACjB,sCAAsC;gBACtC;oBACE,IAAI;wBACF,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,uCAAuC,EAAE,cAAc,CAAC,CAAC,EAAE;4BAC5F,SAAS;gCAAE,eAAe,CAAC,OAAO,EAAE,OAAO;4BAAC;wBAC9C;wBACA,QAAQ,GAAG,CAAC,kCAAkC,SAAS,IAAI;wBAC3D,OAAO;4BAAE,SAAS;4BAAM,MAAM,SAAS,IAAI;wBAAC;oBAC9C,EAAE,OAAO,OAAO;wBACd,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,MAAM,OAAO,EAAE;wBACjD,OAAO;4BAAE,SAAS;wBAAM;oBAC1B;gBACF;gBAEA,sCAAsC;gBACtC;oBACE,IAAI;wBACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,uCAAuC,EAAE,cAAc,QAAQ,CAAC,EAAE,CAAC,GAAG;4BACrG,SAAS;gCAAE,eAAe,CAAC,OAAO,EAAE,OAAO;4BAAC;wBAC9C;wBACA,QAAQ,GAAG,CAAC,2BAA2B,SAAS,IAAI;wBACpD,OAAO;4BAAE,SAAS;4BAAM,MAAM,SAAS,IAAI;wBAAC;oBAC9C,EAAE,OAAO,OAAO;wBACd,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,MAAM,OAAO,EAAE;wBACjD,OAAO;4BAAE,SAAS;wBAAM;oBAC1B;gBACF;gBAEA,0DAA0D;gBAC1D;oBACE,IAAI;wBACF,MAAM,WAAW,MAAM,IAAI,KAAK,CAAC,sBAAsB;4BACrD,oBAAoB;wBACtB,GAAG;4BACD,SAAS;gCAAE,eAAe,CAAC,OAAO,EAAE,OAAO;4BAAC;wBAC9C;wBACA,QAAQ,GAAG,CAAC,6BAA6B,SAAS,IAAI;wBACtD,OAAO;4BAAE,SAAS;4BAAM,MAAM,SAAS,IAAI;wBAAC;oBAC9C,EAAE,OAAO,OAAO;wBACd,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,MAAM,OAAO,EAAE;wBACjD,OAAO;4BAAE,SAAS;wBAAM;oBAC1B;gBACF;gBAEA,wDAAwD;gBACxD;oBACE,IAAI;wBACF,MAAM,WAAW,MAAM,IAAI,KAAK,CAAC,sBAAsB;4BACrD,oBAAoB;wBACtB,GAAG;4BACD,SAAS;gCAAE,eAAe,CAAC,OAAO,EAAE,OAAO;4BAAC;wBAC9C;wBACA,QAAQ,GAAG,CAAC,kCAAkC,SAAS,IAAI;wBAC3D,OAAO;4BAAE,SAAS;4BAAM,MAAM,SAAS,IAAI;wBAAC;oBAC9C,EAAE,OAAO,OAAO;wBACd,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,MAAM,OAAO,EAAE;wBACjD,OAAO;4BAAE,SAAS;wBAAM;oBAC1B;gBACF;aACD;YAED,mDAAmD;YACnD,KAAK,MAAM,YAAY,WAAY;gBACjC,MAAM,SAAS,MAAM;gBACrB,IAAI,OAAO,OAAO,EAAE;oBAClB,UAAU;oBACV;gBACF;YACF;YAEA,mFAAmF;YACnF,uCAAmC;;YAoBnC;YAEA,OAAO;gBAAE;gBAAS,SAAS,UAAU,qCAAqC;YAAqD;QACjI,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;YACnF,mEAAmE;YACnE,yFAAyF;YACzF,OAAO;gBACL,SAAS;gBACT,QAAQ;gBACR,OAAO,MAAM,OAAO;gBACpB,QAAQ,MAAM,QAAQ,EAAE;gBACxB,SAAS;YACX;QACF;IACF;IAEA,0BAA0B;IAC1B,uBAAuB;QACrB,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,OAAO,IAAI,GAAG,CAAC,kDAAkD;YAC/D,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAC9C,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;IAEA,4BAA4B;IAC5B,yBAAyB,OAAO,MAAM,UAAU;QAC9C,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD;QACzB,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,kBAAkB;QAClC,SAAS,MAAM,CAAC,YAAY;QAC5B,SAAS,MAAM,CAAC,QAAQ;QAExB,OAAO,IAAI,IAAI,CAAC,wDAAwD,UAAU;YAChF,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAChC,gBAAgB;YAClB;QACF,GAAG,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACrC;AACF", "debugId": null}}, {"offset": {"line": 1230, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/app/profile/ResumeModal.jsx"], "sourcesContent": ["'use client';\r\nimport { useState, useRef, useEffect } from 'react';\r\nimport { FaTrash, FaFileAlt, FaTimesCircle, FaUpload, FaExternalLinkAlt, FaSpinner, FaCheckCircle, FaExclamationCircle, FaSave } from 'react-icons/fa';\r\nimport { studentsAPI } from '../../api/students';\r\n\r\nexport default function ResumeModal({ isOpen, onClose, resume, onUpload, onDelete }) {\r\n  const [resumes, setResumes] = useState([]);\r\n  const [uploading, setUploading] = useState(false);\r\n  const [loading, setLoading] = useState(false);\r\n  const fileInputRef = useRef(null);\r\n\r\n  // Fetch resumes from backend when modal opens\r\n  useEffect(() => {\r\n    if (isOpen) {\r\n      fetchResumes();\r\n    }\r\n  }, [isOpen]);\r\n\r\n  const fetchResumes = async () => {\r\n    try {\r\n      setLoading(true);\r\n      \r\n      // Check if user is authenticated before proceeding\r\n      const token = localStorage.getItem('access_token');\r\n      if (!token) {\r\n        console.error('No authentication token found');\r\n        setResumes([]);\r\n        setLoading(false);\r\n        return;\r\n      }\r\n      \r\n      // Try to fetch resumes from the new API endpoint with explicit user context\r\n      let resumesData = [];\r\n      try {\r\n        console.log('Fetching user-specific resumes...');\r\n        resumesData = await studentsAPI.getResumes();\r\n        console.log('Resumes fetched:', resumesData);\r\n        \r\n        // Verify we have user-specific data\r\n        if (!Array.isArray(resumesData)) {\r\n          console.error('Invalid resume data format:', resumesData);\r\n          throw new Error('Invalid resume data format');\r\n        }\r\n      } catch (apiError) {\r\n        console.log('New resumes API not available, falling back to profile data:', apiError);\r\n        \r\n        // Fallback: try to get resume from profile\r\n        try {\r\n          const profile = await studentsAPI.getProfile();\r\n          console.log('User profile fetched for resume fallback:', profile?.id);\r\n          \r\n          if (profile?.resume || profile?.resume_url) {\r\n            const resumeUrl = profile.resume_url || profile.resume;\r\n            const fileName = resumeUrl.split('/').pop() || 'Resume.pdf';\r\n            resumesData = [{\r\n              id: profile.id || 1,\r\n              name: fileName,\r\n              resume_url: resumeUrl,\r\n              uploaded_at: profile.updated_at || new Date().toISOString()\r\n            }];\r\n          }\r\n        } catch (profileError) {\r\n          console.error('Error fetching profile for resume:', profileError);\r\n        }\r\n      }\r\n      \r\n      // Transform backend data to frontend format\r\n      const transformedResumes = resumesData.map((resume, index) => ({\r\n        id: resume.id || index + 1,\r\n        name: resume.name || resume.file_name || resume.resume_url?.split('/').pop() || `Resume ${index + 1}`,\r\n        date: resume.uploaded_at ? new Date(resume.uploaded_at).toLocaleDateString('en-US', {\r\n          month: 'short',\r\n          day: 'numeric',\r\n          year: 'numeric'\r\n        }) : new Date().toLocaleDateString('en-US', {\r\n          month: 'short',\r\n          day: 'numeric',\r\n          year: 'numeric'\r\n        }),\r\n        url: resume.resume_url || resume.file_url || resume.url,\r\n        status: 'success'\r\n      }));\r\n      \r\n      console.log(`Displaying ${transformedResumes.length} resumes for current user`);\r\n      setResumes(transformedResumes);\r\n      \r\n    } catch (error) {\r\n      console.error('Error fetching resumes:', error);\r\n      // Final fallback to using the resume prop if API fails\r\n      if (resume) {\r\n        const resumeArray = [];\r\n        if (typeof resume === 'string' && resume.trim() !== '') {\r\n          const fileNameParts = resume.split('/');\r\n          const fileName = fileNameParts[fileNameParts.length - 1];\r\n          \r\n          resumeArray.push({\r\n            id: 1,\r\n            name: fileName || \"Resume\",\r\n            date: new Date().toLocaleDateString('en-US', {\r\n              month: 'short', \r\n              day: 'numeric', \r\n              year: 'numeric'\r\n            }),\r\n            url: resume,\r\n            status: 'success'\r\n          });\r\n        }\r\n        setResumes(resumeArray);\r\n      } else {\r\n        // Set empty array if no fallback data\r\n        setResumes([]);\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleUpload = async (e) => {\r\n    const file = e.target.files[0];\r\n    if (file) {\r\n      try {\r\n        setUploading(true);\r\n        \r\n        // Verify file size (5MB limit)\r\n        if (file.size > 5 * 1024 * 1024) {\r\n          alert('File size exceeds 5MB limit. Please select a smaller file.');\r\n          setUploading(false);\r\n          return;\r\n        }\r\n        \r\n        // Create a new resume object with initial \"uploading\" status\r\n        const newResume = {\r\n          id: Date.now(),\r\n          name: file.name,\r\n          date: new Date().toLocaleDateString('en-US', { \r\n            month: 'short', \r\n            day: 'numeric', \r\n            year: 'numeric' \r\n          }),\r\n          file: file,\r\n          url: URL.createObjectURL(file), // Temporary URL for preview\r\n          status: 'uploading',\r\n          progress: 0\r\n        };\r\n        \r\n        // Add the new resume to the existing list\r\n        setResumes(prevResumes => [...prevResumes, newResume]);\r\n        \r\n        // Simulate progress updates\r\n        const progressInterval = setInterval(() => {\r\n          setResumes(prevResumes => prevResumes.map(r => \r\n            r.id === newResume.id ? {...r, progress: Math.min(r.progress + 25, 99)} : r\r\n          ));\r\n        }, 500);\r\n        \r\n        try {\r\n          // Upload the file to the server using the new API\r\n          await studentsAPI.uploadResume(file, file.name, false);\r\n\r\n          // Clear interval and refresh the resumes list\r\n          clearInterval(progressInterval);\r\n\r\n          // Refresh resumes from backend (non-blocking)\r\n          fetchResumes().catch(err => console.log('Resume refresh failed after upload:', err));\r\n          \r\n        } catch (error) {\r\n          clearInterval(progressInterval);\r\n          setResumes(prevResumes => prevResumes.map(r => \r\n            r.id === newResume.id ? {...r, status: 'error', progress: 0} : r\r\n          ));\r\n          throw error;\r\n        }\r\n        \r\n        setUploading(false);\r\n      } catch (error) {\r\n        console.error('Error uploading resume:', error);\r\n        setUploading(false);\r\n        alert('Failed to upload resume. Please try again.');\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleViewResume = (url) => {\r\n    if (!url) {\r\n      alert('Resume URL is not available');\r\n      return;\r\n    }\r\n    \r\n    // Special handling for different URL types\r\n    if (url.startsWith('blob:')) {\r\n      // Blob URLs should be used as is\r\n      window.open(url, '_blank');\r\n    } else if (!url.startsWith('http://') && !url.startsWith('https://')) {\r\n      // Handle relative URLs by prepending the origin\r\n      const fullUrl = `${window.location.origin}${url.startsWith('/') ? '' : '/'}${url}`;\r\n      window.open(fullUrl, '_blank');\r\n    } else {\r\n      // Absolute URLs can be used directly\r\n      window.open(url, '_blank');\r\n    }\r\n  };\r\n\r\n  const handleSave = async (resume) => {\r\n    try {\r\n      // Create a download link for the resume\r\n      if (resume.url) {\r\n        const link = document.createElement('a');\r\n        link.href = resume.url;\r\n        link.download = resume.name || 'resume.pdf';\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        document.body.removeChild(link);\r\n      } else {\r\n        alert('Resume file is not available for download');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error downloading resume:', error);\r\n      alert('Failed to download resume. Please try again.');\r\n    }\r\n  };\r\n\r\n  const handleDelete = async (id) => {\r\n    try {\r\n      const resumeToDelete = resumes.find(r => r.id === id);\r\n      \r\n      // Remove from local state immediately for better UX\r\n      setResumes(prevResumes => prevResumes.filter(resume => resume.id !== id));\r\n      \r\n      // Call backend delete if resume has a valid backend ID\r\n      if (resumeToDelete && resumeToDelete.id && typeof resumeToDelete.id === 'number') {\r\n        try {\r\n          const result = await studentsAPI.deleteResume(resumeToDelete.id);\r\n          console.log('Resume deletion response:', result);\r\n          \r\n          // Even if the server returns an error, we'll keep the UI updated\r\n          // The important thing is the user experience - they expect the file to be gone\r\n          \r\n          // Clear any local storage cache that might contain resume data\r\n          if (typeof window !== 'undefined') {\r\n            try {\r\n              // Clear all resume-related data from localStorage\r\n              const localStorageKeys = Object.keys(localStorage);\r\n              const resumeKeys = localStorageKeys.filter(key => \r\n                key.includes('resume') || key.includes('file') || key.includes('document')\r\n              );\r\n              \r\n              // Log the keys we're removing for debugging\r\n              if (resumeKeys.length > 0) {\r\n                console.log('Clearing resume-related localStorage items:', resumeKeys);\r\n                resumeKeys.forEach(key => localStorage.removeItem(key));\r\n              }\r\n              \r\n              // Also clear some specific caches that might be used\r\n              localStorage.removeItem('resume_count');\r\n              localStorage.removeItem('last_resume_update');\r\n              \r\n              // Update the user profile cache to remove the resume if applicable\r\n              const profileCache = localStorage.getItem('user_profile');\r\n              if (profileCache) {\r\n                try {\r\n                  const profile = JSON.parse(profileCache);\r\n                  if (profile && profile.resume) {\r\n                    profile.resume = null;\r\n                    localStorage.setItem('user_profile', JSON.stringify(profile));\r\n                  }\r\n                } catch (e) {\r\n                  // Ignore JSON parse errors\r\n                }\r\n              }\r\n            } catch (e) {\r\n              console.error('Error clearing localStorage:', e);\r\n            }\r\n          }\r\n        } catch (error) {\r\n          console.error('Backend delete failed, but UI is updated:', error);\r\n        }\r\n      }\r\n      \r\n      // Call the onDelete callback if provided\r\n      if (typeof onDelete === 'function') {\r\n        try {\r\n          await onDelete(resumeToDelete);\r\n        } catch (callbackError) {\r\n          console.error('onDelete callback error:', callbackError);\r\n        }\r\n      }\r\n      \r\n      // Always force a refresh of the list regardless of success/failure\r\n      // This ensures we're showing the correct state\r\n      fetchResumes().catch(err => console.log('Resume refresh failed after delete:', err));\r\n\r\n    } catch (error) {\r\n      console.error('Error in delete process:', error);\r\n      // Refresh the list to ensure UI is in sync with backend\r\n      fetchResumes().catch(err => console.log('Resume refresh failed after delete error:', err));\r\n    }\r\n  };\r\n\r\n  // Helper to render resume status icon\r\n  const renderStatusIcon = (resume) => {\r\n    if (resume.status === 'uploading') {\r\n      return (\r\n        <div className=\"ml-2 text-blue-500\">\r\n          <FaSpinner className=\"animate-spin\" />\r\n        </div>\r\n      );\r\n    } else if (resume.status === 'success') {\r\n      return (\r\n        <div className=\"ml-2 text-green-500\">\r\n          <FaCheckCircle />\r\n        </div>\r\n      );\r\n    } else if (resume.status === 'error') {\r\n      return (\r\n        <div className=\"ml-2 text-red-500\">\r\n          <FaExclamationCircle />\r\n        </div>\r\n      );\r\n    }\r\n    return null;\r\n  };\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 flex items-center justify-center z-50 p-4\">\r\n      <div className=\"bg-white rounded-lg w-full max-w-2xl shadow-xl\">\r\n        <div className=\"flex justify-between items-center p-6 border-b\">\r\n          <div>\r\n            <h2 className=\"text-xl font-semibold text-gray-800\">My Resumes</h2>\r\n            <p className=\"text-sm text-gray-500\">Upload multiple resumes for different job types</p>\r\n          </div>\r\n          <button \r\n            onClick={onClose}\r\n            className=\"text-gray-500 hover:text-gray-700\"\r\n          >\r\n            <FaTimesCircle size={24} />\r\n          </button>\r\n        </div>\r\n        \r\n        <div className=\"p-6 max-h-96 overflow-y-auto\">\r\n          {loading ? (\r\n            <div className=\"flex items-center justify-center py-8\">\r\n              <FaSpinner className=\"animate-spin text-blue-500 text-2xl mr-3\" />\r\n              <span className=\"text-gray-600\">Loading resumes...</span>\r\n            </div>\r\n          ) : resumes.length === 0 ? (\r\n            <div className=\"text-center py-8 text-gray-500\">\r\n              <p>No resumes uploaded yet</p>\r\n              <p className=\"text-sm mt-2\">You can upload multiple resumes for different job applications</p>\r\n            </div>\r\n          ) : (\r\n            <div>\r\n              <div className=\"flex justify-between items-center mb-4\">\r\n                <h3 className=\"text-md font-medium text-gray-700\">Your Resumes ({resumes.length})</h3>\r\n                <div className=\"text-sm text-gray-500\">\r\n                  {resumes.length > 1 ? \"You can use different resumes for different applications\" : \"\"}\r\n                </div>\r\n              </div>\r\n              <div className=\"space-y-4\">\r\n                {resumes.map((resume) => (\r\n                  <div \r\n                    key={resume.id} \r\n                    className=\"flex items-center justify-between bg-gray-50 p-4 rounded-lg\"\r\n                  >\r\n                    <div \r\n                      className=\"flex items-center flex-grow cursor-pointer hover:bg-gray-100 p-2 rounded\"\r\n                      onClick={() => resume.status !== 'uploading' ? handleViewResume(resume.url) : null}\r\n                    >\r\n                      <div className=\"bg-blue-100 p-3 rounded-full mr-4\">\r\n                        <FaFileAlt className=\"text-blue-600\" />\r\n                      </div>\r\n                      <div className=\"flex-grow\">\r\n                        <div className=\"flex items-center\">\r\n                          <h3 className=\"font-medium text-gray-800 mr-2\">{resume.name}</h3>\r\n                          {renderStatusIcon(resume)}\r\n                          {resume.status !== 'uploading' && <FaExternalLinkAlt className=\"text-gray-500 text-xs ml-2\" />}\r\n                        </div>\r\n                        <p className=\"text-sm text-gray-500\">Uploaded on {resume.date}</p>\r\n                        \r\n                        {/* Progress bar for uploading resumes */}\r\n                        {resume.status === 'uploading' && (\r\n                          <div className=\"w-full bg-gray-200 rounded-full h-2.5 mt-2\">\r\n                            <div \r\n                              className=\"bg-blue-600 h-2.5 rounded-full transition-all duration-300\"\r\n                              style={{ width: `${resume.progress}%` }}\r\n                            ></div>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      {/* Save/Download Button */}\r\n                      <button\r\n                        onClick={() => handleSave(resume)}\r\n                        className=\"p-2 text-green-500 hover:text-green-700 hover:bg-green-50 rounded-full\"\r\n                        aria-label=\"Download resume\"\r\n                        disabled={resume.status === 'uploading'}\r\n                        title=\"Download resume\"\r\n                      >\r\n                        <FaSave className={resume.status === 'uploading' ? 'opacity-50' : ''} />\r\n                      </button>\r\n\r\n                      {/* Delete Button */}\r\n                      <button\r\n                        onClick={() => handleDelete(resume.id)}\r\n                        className=\"p-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full\"\r\n                        aria-label=\"Delete resume\"\r\n                        disabled={resume.status === 'uploading'}\r\n                        title=\"Delete resume\"\r\n                      >\r\n                        <FaTrash className={resume.status === 'uploading' ? 'opacity-50' : ''} />\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n        \r\n        <div className=\"border-t p-6 flex justify-between items-center\">\r\n          <div>\r\n            <p className=\"text-sm text-gray-500\">\r\n              Supported formats: PDF, DOCX (max 5MB)\r\n            </p>\r\n            <p className=\"text-xs text-gray-400 mt-1\">\r\n              You can upload multiple resumes tailored to different positions\r\n            </p>\r\n          </div>\r\n          <div>\r\n            <input \r\n              type=\"file\" \r\n              accept=\".pdf,.docx\" \r\n              className=\"hidden\" \r\n              ref={fileInputRef}\r\n              onChange={handleUpload}\r\n              disabled={uploading}\r\n            />\r\n            <button \r\n              onClick={() => fileInputRef.current.click()}\r\n              className={`bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center ${uploading ? 'opacity-70 cursor-not-allowed' : ''}`}\r\n              disabled={uploading}\r\n            >\r\n              {uploading ? (\r\n                <>\r\n                  <FaSpinner className=\"mr-2 animate-spin\" /> Uploading...\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <FaUpload className=\"mr-2\" /> Add Resume\r\n                </>\r\n              )}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n          //     )}\r\n          //   </button>\r\n          // </div>\r\n\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;AAKe,SAAS,YAAY,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE;IACjF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE5B,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV;QACF;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YAEX,mDAAmD;YACnD,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,CAAC,OAAO;gBACV,QAAQ,KAAK,CAAC;gBACd,WAAW,EAAE;gBACb,WAAW;gBACX;YACF;YAEA,4EAA4E;YAC5E,IAAI,cAAc,EAAE;YACpB,IAAI;gBACF,QAAQ,GAAG,CAAC;gBACZ,cAAc,MAAM,sHAAA,CAAA,cAAW,CAAC,UAAU;gBAC1C,QAAQ,GAAG,CAAC,oBAAoB;gBAEhC,oCAAoC;gBACpC,IAAI,CAAC,MAAM,OAAO,CAAC,cAAc;oBAC/B,QAAQ,KAAK,CAAC,+BAA+B;oBAC7C,MAAM,IAAI,MAAM;gBAClB;YACF,EAAE,OAAO,UAAU;gBACjB,QAAQ,GAAG,CAAC,gEAAgE;gBAE5E,2CAA2C;gBAC3C,IAAI;oBACF,MAAM,UAAU,MAAM,sHAAA,CAAA,cAAW,CAAC,UAAU;oBAC5C,QAAQ,GAAG,CAAC,6CAA6C,SAAS;oBAElE,IAAI,SAAS,UAAU,SAAS,YAAY;wBAC1C,MAAM,YAAY,QAAQ,UAAU,IAAI,QAAQ,MAAM;wBACtD,MAAM,WAAW,UAAU,KAAK,CAAC,KAAK,GAAG,MAAM;wBAC/C,cAAc;4BAAC;gCACb,IAAI,QAAQ,EAAE,IAAI;gCAClB,MAAM;gCACN,YAAY;gCACZ,aAAa,QAAQ,UAAU,IAAI,IAAI,OAAO,WAAW;4BAC3D;yBAAE;oBACJ;gBACF,EAAE,OAAO,cAAc;oBACrB,QAAQ,KAAK,CAAC,sCAAsC;gBACtD;YACF;YAEA,4CAA4C;YAC5C,MAAM,qBAAqB,YAAY,GAAG,CAAC,CAAC,QAAQ,QAAU,CAAC;oBAC7D,IAAI,OAAO,EAAE,IAAI,QAAQ;oBACzB,MAAM,OAAO,IAAI,IAAI,OAAO,SAAS,IAAI,OAAO,UAAU,EAAE,MAAM,KAAK,SAAS,CAAC,OAAO,EAAE,QAAQ,GAAG;oBACrG,MAAM,OAAO,WAAW,GAAG,IAAI,KAAK,OAAO,WAAW,EAAE,kBAAkB,CAAC,SAAS;wBAClF,OAAO;wBACP,KAAK;wBACL,MAAM;oBACR,KAAK,IAAI,OAAO,kBAAkB,CAAC,SAAS;wBAC1C,OAAO;wBACP,KAAK;wBACL,MAAM;oBACR;oBACA,KAAK,OAAO,UAAU,IAAI,OAAO,QAAQ,IAAI,OAAO,GAAG;oBACvD,QAAQ;gBACV,CAAC;YAED,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,mBAAmB,MAAM,CAAC,yBAAyB,CAAC;YAC9E,WAAW;QAEb,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,uDAAuD;YACvD,IAAI,QAAQ;gBACV,MAAM,cAAc,EAAE;gBACtB,IAAI,OAAO,WAAW,YAAY,OAAO,IAAI,OAAO,IAAI;oBACtD,MAAM,gBAAgB,OAAO,KAAK,CAAC;oBACnC,MAAM,WAAW,aAAa,CAAC,cAAc,MAAM,GAAG,EAAE;oBAExD,YAAY,IAAI,CAAC;wBACf,IAAI;wBACJ,MAAM,YAAY;wBAClB,MAAM,IAAI,OAAO,kBAAkB,CAAC,SAAS;4BAC3C,OAAO;4BACP,KAAK;4BACL,MAAM;wBACR;wBACA,KAAK;wBACL,QAAQ;oBACV;gBACF;gBACA,WAAW;YACb,OAAO;gBACL,sCAAsC;gBACtC,WAAW,EAAE;YACf;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;QAC9B,IAAI,MAAM;YACR,IAAI;gBACF,aAAa;gBAEb,+BAA+B;gBAC/B,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;oBAC/B,MAAM;oBACN,aAAa;oBACb;gBACF;gBAEA,6DAA6D;gBAC7D,MAAM,YAAY;oBAChB,IAAI,KAAK,GAAG;oBACZ,MAAM,KAAK,IAAI;oBACf,MAAM,IAAI,OAAO,kBAAkB,CAAC,SAAS;wBAC3C,OAAO;wBACP,KAAK;wBACL,MAAM;oBACR;oBACA,MAAM;oBACN,KAAK,IAAI,eAAe,CAAC;oBACzB,QAAQ;oBACR,UAAU;gBACZ;gBAEA,0CAA0C;gBAC1C,WAAW,CAAA,cAAe;2BAAI;wBAAa;qBAAU;gBAErD,4BAA4B;gBAC5B,MAAM,mBAAmB,YAAY;oBACnC,WAAW,CAAA,cAAe,YAAY,GAAG,CAAC,CAAA,IACxC,EAAE,EAAE,KAAK,UAAU,EAAE,GAAG;gCAAC,GAAG,CAAC;gCAAE,UAAU,KAAK,GAAG,CAAC,EAAE,QAAQ,GAAG,IAAI;4BAAG,IAAI;gBAE9E,GAAG;gBAEH,IAAI;oBACF,kDAAkD;oBAClD,MAAM,sHAAA,CAAA,cAAW,CAAC,YAAY,CAAC,MAAM,KAAK,IAAI,EAAE;oBAEhD,8CAA8C;oBAC9C,cAAc;oBAEd,8CAA8C;oBAC9C,eAAe,KAAK,CAAC,CAAA,MAAO,QAAQ,GAAG,CAAC,uCAAuC;gBAEjF,EAAE,OAAO,OAAO;oBACd,cAAc;oBACd,WAAW,CAAA,cAAe,YAAY,GAAG,CAAC,CAAA,IACxC,EAAE,EAAE,KAAK,UAAU,EAAE,GAAG;gCAAC,GAAG,CAAC;gCAAE,QAAQ;gCAAS,UAAU;4BAAC,IAAI;oBAEjE,MAAM;gBACR;gBAEA,aAAa;YACf,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,aAAa;gBACb,MAAM;YACR;QACF;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,KAAK;YACR,MAAM;YACN;QACF;QAEA,2CAA2C;QAC3C,IAAI,IAAI,UAAU,CAAC,UAAU;YAC3B,iCAAiC;YACjC,OAAO,IAAI,CAAC,KAAK;QACnB,OAAO,IAAI,CAAC,IAAI,UAAU,CAAC,cAAc,CAAC,IAAI,UAAU,CAAC,aAAa;YACpE,gDAAgD;YAChD,MAAM,UAAU,GAAG,OAAO,QAAQ,CAAC,MAAM,GAAG,IAAI,UAAU,CAAC,OAAO,KAAK,MAAM,KAAK;YAClF,OAAO,IAAI,CAAC,SAAS;QACvB,OAAO;YACL,qCAAqC;YACrC,OAAO,IAAI,CAAC,KAAK;QACnB;IACF;IAEA,MAAM,aAAa,OAAO;QACxB,IAAI;YACF,wCAAwC;YACxC,IAAI,OAAO,GAAG,EAAE;gBACd,MAAM,OAAO,SAAS,aAAa,CAAC;gBACpC,KAAK,IAAI,GAAG,OAAO,GAAG;gBACtB,KAAK,QAAQ,GAAG,OAAO,IAAI,IAAI;gBAC/B,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,KAAK,KAAK;gBACV,SAAS,IAAI,CAAC,WAAW,CAAC;YAC5B,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,iBAAiB,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAElD,oDAAoD;YACpD,WAAW,CAAA,cAAe,YAAY,MAAM,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;YAErE,uDAAuD;YACvD,IAAI,kBAAkB,eAAe,EAAE,IAAI,OAAO,eAAe,EAAE,KAAK,UAAU;gBAChF,IAAI;oBACF,MAAM,SAAS,MAAM,sHAAA,CAAA,cAAW,CAAC,YAAY,CAAC,eAAe,EAAE;oBAC/D,QAAQ,GAAG,CAAC,6BAA6B;oBAEzC,iEAAiE;oBACjE,+EAA+E;oBAE/E,+DAA+D;oBAC/D,uCAAmC;;oBAkCnC;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,6CAA6C;gBAC7D;YACF;YAEA,yCAAyC;YACzC,IAAI,OAAO,aAAa,YAAY;gBAClC,IAAI;oBACF,MAAM,SAAS;gBACjB,EAAE,OAAO,eAAe;oBACtB,QAAQ,KAAK,CAAC,4BAA4B;gBAC5C;YACF;YAEA,mEAAmE;YACnE,+CAA+C;YAC/C,eAAe,KAAK,CAAC,CAAA,MAAO,QAAQ,GAAG,CAAC,uCAAuC;QAEjF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,wDAAwD;YACxD,eAAe,KAAK,CAAC,CAAA,MAAO,QAAQ,GAAG,CAAC,6CAA6C;QACvF;IACF;IAEA,sCAAsC;IACtC,MAAM,mBAAmB,CAAC;QACxB,IAAI,OAAO,MAAM,KAAK,aAAa;YACjC,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,8IAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;;;;;;QAG3B,OAAO,IAAI,OAAO,MAAM,KAAK,WAAW;YACtC,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,8IAAA,CAAA,gBAAa;;;;;;;;;;QAGpB,OAAO,IAAI,OAAO,MAAM,KAAK,SAAS;YACpC,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,8IAAA,CAAA,sBAAmB;;;;;;;;;;QAG1B;QACA,OAAO;IACT;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAEvC,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,8IAAA,CAAA,gBAAa;gCAAC,MAAM;;;;;;;;;;;;;;;;;8BAIzB,8OAAC;oBAAI,WAAU;8BACZ,wBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8IAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;+BAEhC,QAAQ,MAAM,KAAK,kBACrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAE;;;;;;0CACH,8OAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;6CAG9B,8OAAC;;0CACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;4CAAoC;4CAAe,QAAQ,MAAM;4CAAC;;;;;;;kDAChF,8OAAC;wCAAI,WAAU;kDACZ,QAAQ,MAAM,GAAG,IAAI,6DAA6D;;;;;;;;;;;;0CAGvF,8OAAC;gCAAI,WAAU;0CACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;wCAEC,WAAU;;0DAEV,8OAAC;gDACC,WAAU;gDACV,SAAS,IAAM,OAAO,MAAM,KAAK,cAAc,iBAAiB,OAAO,GAAG,IAAI;;kEAE9E,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,8IAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;;;;;kEAEvB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAAkC,OAAO,IAAI;;;;;;oEAC1D,iBAAiB;oEACjB,OAAO,MAAM,KAAK,6BAAe,8OAAC,8IAAA,CAAA,oBAAiB;wEAAC,WAAU;;;;;;;;;;;;0EAEjE,8OAAC;gEAAE,WAAU;;oEAAwB;oEAAa,OAAO,IAAI;;;;;;;4DAG5D,OAAO,MAAM,KAAK,6BACjB,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEACC,WAAU;oEACV,OAAO;wEAAE,OAAO,GAAG,OAAO,QAAQ,CAAC,CAAC,CAAC;oEAAC;;;;;;;;;;;;;;;;;;;;;;;0DAMhD,8OAAC;gDAAI,WAAU;;kEAEb,8OAAC;wDACC,SAAS,IAAM,WAAW;wDAC1B,WAAU;wDACV,cAAW;wDACX,UAAU,OAAO,MAAM,KAAK;wDAC5B,OAAM;kEAEN,cAAA,8OAAC,8IAAA,CAAA,SAAM;4DAAC,WAAW,OAAO,MAAM,KAAK,cAAc,eAAe;;;;;;;;;;;kEAIpE,8OAAC;wDACC,SAAS,IAAM,aAAa,OAAO,EAAE;wDACrC,WAAU;wDACV,cAAW;wDACX,UAAU,OAAO,MAAM,KAAK;wDAC5B,OAAM;kEAEN,cAAA,8OAAC,8IAAA,CAAA,UAAO;4DAAC,WAAW,OAAO,MAAM,KAAK,cAAc,eAAe;;;;;;;;;;;;;;;;;;uCAjDlE,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;8BA2D1B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;8CAGrC,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAI5C,8OAAC;;8CACC,8OAAC;oCACC,MAAK;oCACL,QAAO;oCACP,WAAU;oCACV,KAAK;oCACL,UAAU;oCACV,UAAU;;;;;;8CAEZ,8OAAC;oCACC,SAAS,IAAM,aAAa,OAAO,CAAC,KAAK;oCACzC,WAAW,CAAC,gFAAgF,EAAE,YAAY,kCAAkC,IAAI;oCAChJ,UAAU;8CAET,0BACC;;0DACE,8OAAC,8IAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAsB;;qEAG7C;;0DACE,8OAAC,8IAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/C,EACU,SAAS;CACT,cAAc;CACd,SAAS", "debugId": null}}, {"offset": {"line": 1922, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/app/profile/DocumentsModal.jsx"], "sourcesContent": ["'use client';\r\nimport { useState, useRef, useEffect } from 'react';\r\nimport { FaTrash, FaFileAlt, FaTimesCircle, FaUpload, FaExternalLinkAlt, FaSpinner, FaSave } from 'react-icons/fa';\r\n\r\nexport default function DocumentsModal({\r\n  isOpen,\r\n  onClose,\r\n  documents = {},\r\n  onUploadCertificate,\r\n  onUploadMarksheet,\r\n  onDeleteCertificate,\r\n  onDeleteMarksheet\r\n}) {\r\n  const [activeTab, setActiveTab] = useState('tenth');\r\n  const [uploading, setUploading] = useState(false);\r\n  const [documentState, setDocumentState] = useState({\r\n    tenth: [],\r\n    twelfth: [],\r\n    semesterwise: []\r\n  });\r\n  \r\n  const fileInputRef = useRef(null);\r\n  const semesterRef = useRef(null);\r\n  const cgpaRef = useRef(null);\r\n\r\n  // Function to format URLs properly\r\n  const getFormattedUrl = (url) => {\r\n    if (!url) return null;\r\n    \r\n    // Check if URL is relative (doesn't start with http)\r\n    if (url && !url.startsWith('http')) {\r\n      // Prepend the base URL for local development\r\n      return `http://localhost:8000${url}`;\r\n    }\r\n    return url;\r\n  };\r\n\r\n  // Initialize with documents from backend if available\r\n  useEffect(() => {\r\n    const newState = { tenth: [], twelfth: [], semesterwise: [] };\r\n\r\n    // Format 10th certificate - only if it actually exists and is not empty\r\n    if (documents.tenth && documents.tenth.trim() !== '' && documents.tenth !== 'null' && documents.tenth !== 'undefined') {\r\n      const fileNameParts = typeof documents.tenth === 'string' ? documents.tenth.split('/') : ['10th Certificate'];\r\n      const fileName = fileNameParts[fileNameParts.length - 1];\r\n\r\n      // Only add if we have a valid filename\r\n      if (fileName && fileName !== '' && fileName !== 'null') {\r\n        newState.tenth = [{\r\n          id: 1,\r\n          name: fileName,\r\n          date: new Date().toLocaleDateString('en-US', {\r\n            month: 'short', day: 'numeric', year: 'numeric'\r\n          }),\r\n          url: documents.tenth\r\n        }];\r\n      }\r\n    }\r\n\r\n    // Format 12th certificate - only if it actually exists and is not empty\r\n    if (documents.twelfth && documents.twelfth.trim() !== '' && documents.twelfth !== 'null' && documents.twelfth !== 'undefined') {\r\n      const fileNameParts = typeof documents.twelfth === 'string' ? documents.twelfth.split('/') : ['12th Certificate'];\r\n      const fileName = fileNameParts[fileNameParts.length - 1];\r\n\r\n      // Only add if we have a valid filename\r\n      if (fileName && fileName !== '' && fileName !== 'null') {\r\n        newState.twelfth = [{\r\n          id: 1,\r\n          name: fileName,\r\n          date: new Date().toLocaleDateString('en-US', {\r\n            month: 'short', day: 'numeric', year: 'numeric'\r\n          }),\r\n          url: documents.twelfth\r\n        }];\r\n      }\r\n    }\r\n\r\n    // Format semester marksheets - only if they actually exist\r\n    if (documents.semesterMarksheets && Array.isArray(documents.semesterMarksheets) && documents.semesterMarksheets.length > 0) {\r\n      newState.semesterwise = documents.semesterMarksheets\r\n        .filter(sheet => sheet && sheet.marksheet_url && sheet.marksheet_url.trim() !== '')\r\n        .map(sheet => ({\r\n          id: sheet.id,\r\n          name: `Semester ${sheet.semester} Marksheet (CGPA: ${sheet.cgpa})`,\r\n          date: sheet.upload_date ? new Date(sheet.upload_date).toLocaleDateString('en-US', {\r\n            month: 'short', day: 'numeric', year: 'numeric'\r\n          }) : 'Unknown date',\r\n          url: sheet.marksheet_url || sheet.marksheet_file,\r\n          semester: sheet.semester,\r\n          cgpa: sheet.cgpa\r\n        }));\r\n    }\r\n\r\n    setDocumentState(newState);\r\n  }, [documents]);\r\n\r\n  const handleUpload = async (e) => {\r\n    const file = e.target.files[0];\r\n    if (!file) return;\r\n    \r\n    try {\r\n      setUploading(true);\r\n      \r\n      if (activeTab === 'tenth') {\r\n        await onUploadCertificate(file, 'tenth');\r\n        const newDoc = {\r\n          id: Date.now(),\r\n          name: file.name,\r\n          date: new Date().toLocaleDateString('en-US', {\r\n            month: 'short', day: 'numeric', year: 'numeric'\r\n          }),\r\n          url: URL.createObjectURL(file) // Temporary URL for preview\r\n        };\r\n        setDocumentState(prev => ({ ...prev, tenth: [newDoc] }));\r\n      }\r\n      else if (activeTab === 'twelfth') {\r\n        await onUploadCertificate(file, 'twelfth');\r\n        const newDoc = {\r\n          id: Date.now(),\r\n          name: file.name,\r\n          date: new Date().toLocaleDateString('en-US', {\r\n            month: 'short', day: 'numeric', year: 'numeric'\r\n          }),\r\n          url: URL.createObjectURL(file) // Temporary URL for preview\r\n        };\r\n        setDocumentState(prev => ({ ...prev, twelfth: [newDoc] }));\r\n      }\r\n      else if (activeTab === 'semesterwise') {\r\n        // Safety check for refs\r\n        if (!semesterRef.current || !cgpaRef.current) {\r\n          alert('Semester input fields are not available');\r\n          setUploading(false);\r\n          return;\r\n        }\r\n\r\n        const semester = semesterRef.current.value;\r\n        const cgpa = cgpaRef.current.value;\r\n\r\n        if (!semester || !cgpa) {\r\n          alert('Please enter semester number and CGPA');\r\n          setUploading(false);\r\n          return;\r\n        }\r\n        \r\n        await onUploadMarksheet(file, semester, cgpa);\r\n        const newDoc = {\r\n          id: Date.now(),\r\n          name: `Semester ${semester} Marksheet (CGPA: ${cgpa})`,\r\n          date: new Date().toLocaleDateString('en-US', {\r\n            month: 'short', day: 'numeric', year: 'numeric'\r\n          }),\r\n          url: URL.createObjectURL(file), // Temporary URL for preview\r\n          semester,\r\n          cgpa\r\n        };\r\n        setDocumentState(prev => ({ \r\n          ...prev, \r\n          semesterwise: [...prev.semesterwise, newDoc] \r\n        }));\r\n      }\r\n      \r\n      setUploading(false);\r\n    } catch (error) {\r\n      console.error('Error uploading document:', error);\r\n      setUploading(false);\r\n      alert('Failed to upload document. Please try again.');\r\n    }\r\n  };\r\n\r\n  const handleViewDocument = (url) => {\r\n    if (!url) {\r\n      alert('Document URL is not available');\r\n      return;\r\n    }\r\n\r\n    const formattedUrl = getFormattedUrl(url);\r\n    window.open(formattedUrl, '_blank');\r\n  };\r\n\r\n  const handleSaveDocument = (document) => {\r\n    try {\r\n      if (document.url) {\r\n        const formattedUrl = getFormattedUrl(document.url);\r\n        const link = document.createElement('a');\r\n        link.href = formattedUrl;\r\n        link.download = document.name || 'document.pdf';\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        document.body.removeChild(link);\r\n      } else {\r\n        alert('Document file is not available for download');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error downloading document:', error);\r\n      alert('Failed to download document. Please try again.');\r\n    }\r\n  };\r\n\r\n  const handleDelete = async (id, documentType = null) => {\r\n    try {\r\n      if (activeTab === 'tenth' || activeTab === 'twelfth') {\r\n        // Delete certificate\r\n        const certType = activeTab === 'tenth' ? '10th' : '12th';\r\n        await onDeleteCertificate(certType);\r\n\r\n        // Update local state\r\n        setDocumentState(prev => ({\r\n          ...prev,\r\n          [activeTab]: []\r\n        }));\r\n      } else if (activeTab === 'semesterwise' && documentType) {\r\n        // Delete marksheet\r\n        await onDeleteMarksheet(documentType.semester);\r\n\r\n        // Update local state\r\n        setDocumentState(prev => ({\r\n          ...prev,\r\n          semesterwise: prev.semesterwise.filter(doc => doc.semester !== documentType.semester)\r\n        }));\r\n      } else {\r\n        // Fallback to local state update only\r\n        setDocumentState(prev => ({\r\n          ...prev,\r\n          [activeTab]: prev[activeTab].filter(doc => doc.id !== id)\r\n        }));\r\n      }\r\n    } catch (error) {\r\n      console.error('Error deleting document:', error);\r\n      alert('Failed to delete document. Please try again.');\r\n    }\r\n  };\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 flex items-center justify-center z-50 p-4\">\r\n      <div className=\"bg-white rounded-lg w-full max-w-2xl shadow-xl\">\r\n        <div className=\"flex justify-between items-center p-6 border-b\">\r\n          <h2 className=\"text-xl font-semibold text-gray-800\">My Documents</h2>\r\n          <button \r\n            onClick={onClose}\r\n            className=\"text-gray-500 hover:text-gray-700\"\r\n          >\r\n            <FaTimesCircle size={24} />\r\n          </button>\r\n        </div>\r\n        \r\n        {/* Tabs */}\r\n        <div className=\"flex border-b\">\r\n          <button \r\n            className={`px-6 py-3 text-sm font-medium ${activeTab === 'tenth' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700'}`}\r\n            onClick={() => setActiveTab('tenth')}\r\n          >\r\n            10th Certificate\r\n          </button>\r\n          <button \r\n            className={`px-6 py-3 text-sm font-medium ${activeTab === 'twelfth' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700'}`}\r\n            onClick={() => setActiveTab('twelfth')}\r\n          >\r\n            12th Certificate\r\n          </button>\r\n          <button \r\n            className={`px-6 py-3 text-sm font-medium ${activeTab === 'semesterwise' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700'}`}\r\n            onClick={() => setActiveTab('semesterwise')}\r\n          >\r\n            Semester Grades\r\n          </button>\r\n        </div>\r\n        \r\n        <div className=\"p-6 max-h-96 overflow-y-auto\">\r\n          {documentState[activeTab].length === 0 ? (\r\n            <div className=\"text-center py-8 text-gray-500\">\r\n              No documents uploaded for this category yet\r\n            </div>\r\n          ) : (\r\n            <div className=\"space-y-4\">\r\n              {documentState[activeTab].map((document, index) => (\r\n                <div\r\n                  key={document.id || `${activeTab}-${index}`}\r\n                  className=\"flex items-center justify-between bg-gray-50 p-4 rounded-lg\"\r\n                >\r\n                  <div \r\n                    className=\"flex items-center flex-grow cursor-pointer hover:bg-gray-100 p-2 rounded\"\r\n                    onClick={() => handleViewDocument(document.url)}\r\n                  >\r\n                    <div className=\"bg-blue-100 p-3 rounded-full mr-4\">\r\n                      <FaFileAlt className=\"text-blue-600\" />\r\n                    </div>\r\n                    <div>\r\n                      <div className=\"flex items-center\">\r\n                        <h3 className=\"font-medium text-gray-800 mr-2\">{document.name}</h3>\r\n                        <FaExternalLinkAlt className=\"text-gray-500 text-xs\" />\r\n                      </div>\r\n                      <p className=\"text-sm text-gray-500\">Uploaded on {document.date}</p>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    {/* Save/Download Button */}\r\n                    <button\r\n                      onClick={() => handleSaveDocument(document)}\r\n                      className=\"p-2 text-green-500 hover:text-green-700 hover:bg-green-50 rounded-full\"\r\n                      aria-label=\"Download document\"\r\n                      title=\"Download document\"\r\n                    >\r\n                      <FaSave />\r\n                    </button>\r\n\r\n                    {/* Delete Button */}\r\n                    <button\r\n                      onClick={() => handleDelete(document.id, document)}\r\n                      className=\"p-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full\"\r\n                      aria-label=\"Delete document\"\r\n                      title=\"Delete document\"\r\n                    >\r\n                      <FaTrash />\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n        \r\n        <div className=\"border-t p-6\">\r\n          {/* Semester inputs for semesterwise tab */}\r\n          {activeTab === 'semesterwise' && (\r\n            <div className=\"mb-4 grid grid-cols-2 gap-4\">\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                  Semester Number\r\n                </label>\r\n                <input\r\n                  type=\"number\"\r\n                  min=\"1\"\r\n                  max=\"8\"\r\n                  ref={semesterRef}\r\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                  placeholder=\"e.g., 1\"\r\n                />\r\n              </div>\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                  CGPA\r\n                </label>\r\n                <input\r\n                  type=\"number\"\r\n                  step=\"0.01\"\r\n                  min=\"0\"\r\n                  max=\"10\"\r\n                  ref={cgpaRef}\r\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                  placeholder=\"e.g., 8.5\"\r\n                />\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          <div className=\"flex justify-between items-center\">\r\n            <p className=\"text-sm text-gray-500\">\r\n              Supported formats: PDF, JPG, PNG (max 5MB)\r\n            </p>\r\n            <div>\r\n              <input\r\n                type=\"file\"\r\n                accept=\".pdf,.jpg,.jpeg,.png\"\r\n                className=\"hidden\"\r\n                ref={fileInputRef}\r\n                onChange={handleUpload}\r\n                disabled={uploading}\r\n              />\r\n              <button\r\n                onClick={() => fileInputRef.current.click()}\r\n                className={`bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center ${uploading ? 'opacity-70 cursor-not-allowed' : ''}`}\r\n                disabled={uploading}\r\n              >\r\n                {uploading ? (\r\n                  <>\r\n                    <FaSpinner className=\"mr-2 animate-spin\" /> Uploading...\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    <FaUpload className=\"mr-2\" /> Upload Document\r\n                  </>\r\n                )}\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAFA;;;;AAIe,SAAS,eAAe,EACrC,MAAM,EACN,OAAO,EACP,YAAY,CAAC,CAAC,EACd,mBAAmB,EACnB,iBAAiB,EACjB,mBAAmB,EACnB,iBAAiB,EAClB;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACjD,OAAO,EAAE;QACT,SAAS,EAAE;QACX,cAAc,EAAE;IAClB;IAEA,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAEvB,mCAAmC;IACnC,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,KAAK,OAAO;QAEjB,qDAAqD;QACrD,IAAI,OAAO,CAAC,IAAI,UAAU,CAAC,SAAS;YAClC,6CAA6C;YAC7C,OAAO,CAAC,qBAAqB,EAAE,KAAK;QACtC;QACA,OAAO;IACT;IAEA,sDAAsD;IACtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW;YAAE,OAAO,EAAE;YAAE,SAAS,EAAE;YAAE,cAAc,EAAE;QAAC;QAE5D,wEAAwE;QACxE,IAAI,UAAU,KAAK,IAAI,UAAU,KAAK,CAAC,IAAI,OAAO,MAAM,UAAU,KAAK,KAAK,UAAU,UAAU,KAAK,KAAK,aAAa;YACrH,MAAM,gBAAgB,OAAO,UAAU,KAAK,KAAK,WAAW,UAAU,KAAK,CAAC,KAAK,CAAC,OAAO;gBAAC;aAAmB;YAC7G,MAAM,WAAW,aAAa,CAAC,cAAc,MAAM,GAAG,EAAE;YAExD,uCAAuC;YACvC,IAAI,YAAY,aAAa,MAAM,aAAa,QAAQ;gBACtD,SAAS,KAAK,GAAG;oBAAC;wBAChB,IAAI;wBACJ,MAAM;wBACN,MAAM,IAAI,OAAO,kBAAkB,CAAC,SAAS;4BAC3C,OAAO;4BAAS,KAAK;4BAAW,MAAM;wBACxC;wBACA,KAAK,UAAU,KAAK;oBACtB;iBAAE;YACJ;QACF;QAEA,wEAAwE;QACxE,IAAI,UAAU,OAAO,IAAI,UAAU,OAAO,CAAC,IAAI,OAAO,MAAM,UAAU,OAAO,KAAK,UAAU,UAAU,OAAO,KAAK,aAAa;YAC7H,MAAM,gBAAgB,OAAO,UAAU,OAAO,KAAK,WAAW,UAAU,OAAO,CAAC,KAAK,CAAC,OAAO;gBAAC;aAAmB;YACjH,MAAM,WAAW,aAAa,CAAC,cAAc,MAAM,GAAG,EAAE;YAExD,uCAAuC;YACvC,IAAI,YAAY,aAAa,MAAM,aAAa,QAAQ;gBACtD,SAAS,OAAO,GAAG;oBAAC;wBAClB,IAAI;wBACJ,MAAM;wBACN,MAAM,IAAI,OAAO,kBAAkB,CAAC,SAAS;4BAC3C,OAAO;4BAAS,KAAK;4BAAW,MAAM;wBACxC;wBACA,KAAK,UAAU,OAAO;oBACxB;iBAAE;YACJ;QACF;QAEA,2DAA2D;QAC3D,IAAI,UAAU,kBAAkB,IAAI,MAAM,OAAO,CAAC,UAAU,kBAAkB,KAAK,UAAU,kBAAkB,CAAC,MAAM,GAAG,GAAG;YAC1H,SAAS,YAAY,GAAG,UAAU,kBAAkB,CACjD,MAAM,CAAC,CAAA,QAAS,SAAS,MAAM,aAAa,IAAI,MAAM,aAAa,CAAC,IAAI,OAAO,IAC/E,GAAG,CAAC,CAAA,QAAS,CAAC;oBACb,IAAI,MAAM,EAAE;oBACZ,MAAM,CAAC,SAAS,EAAE,MAAM,QAAQ,CAAC,kBAAkB,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC;oBAClE,MAAM,MAAM,WAAW,GAAG,IAAI,KAAK,MAAM,WAAW,EAAE,kBAAkB,CAAC,SAAS;wBAChF,OAAO;wBAAS,KAAK;wBAAW,MAAM;oBACxC,KAAK;oBACL,KAAK,MAAM,aAAa,IAAI,MAAM,cAAc;oBAChD,UAAU,MAAM,QAAQ;oBACxB,MAAM,MAAM,IAAI;gBAClB,CAAC;QACL;QAEA,iBAAiB;IACnB,GAAG;QAAC;KAAU;IAEd,MAAM,eAAe,OAAO;QAC1B,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;QAC9B,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,aAAa;YAEb,IAAI,cAAc,SAAS;gBACzB,MAAM,oBAAoB,MAAM;gBAChC,MAAM,SAAS;oBACb,IAAI,KAAK,GAAG;oBACZ,MAAM,KAAK,IAAI;oBACf,MAAM,IAAI,OAAO,kBAAkB,CAAC,SAAS;wBAC3C,OAAO;wBAAS,KAAK;wBAAW,MAAM;oBACxC;oBACA,KAAK,IAAI,eAAe,CAAC,MAAM,4BAA4B;gBAC7D;gBACA,iBAAiB,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,OAAO;4BAAC;yBAAO;oBAAC,CAAC;YACxD,OACK,IAAI,cAAc,WAAW;gBAChC,MAAM,oBAAoB,MAAM;gBAChC,MAAM,SAAS;oBACb,IAAI,KAAK,GAAG;oBACZ,MAAM,KAAK,IAAI;oBACf,MAAM,IAAI,OAAO,kBAAkB,CAAC,SAAS;wBAC3C,OAAO;wBAAS,KAAK;wBAAW,MAAM;oBACxC;oBACA,KAAK,IAAI,eAAe,CAAC,MAAM,4BAA4B;gBAC7D;gBACA,iBAAiB,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,SAAS;4BAAC;yBAAO;oBAAC,CAAC;YAC1D,OACK,IAAI,cAAc,gBAAgB;gBACrC,wBAAwB;gBACxB,IAAI,CAAC,YAAY,OAAO,IAAI,CAAC,QAAQ,OAAO,EAAE;oBAC5C,MAAM;oBACN,aAAa;oBACb;gBACF;gBAEA,MAAM,WAAW,YAAY,OAAO,CAAC,KAAK;gBAC1C,MAAM,OAAO,QAAQ,OAAO,CAAC,KAAK;gBAElC,IAAI,CAAC,YAAY,CAAC,MAAM;oBACtB,MAAM;oBACN,aAAa;oBACb;gBACF;gBAEA,MAAM,kBAAkB,MAAM,UAAU;gBACxC,MAAM,SAAS;oBACb,IAAI,KAAK,GAAG;oBACZ,MAAM,CAAC,SAAS,EAAE,SAAS,kBAAkB,EAAE,KAAK,CAAC,CAAC;oBACtD,MAAM,IAAI,OAAO,kBAAkB,CAAC,SAAS;wBAC3C,OAAO;wBAAS,KAAK;wBAAW,MAAM;oBACxC;oBACA,KAAK,IAAI,eAAe,CAAC;oBACzB;oBACA;gBACF;gBACA,iBAAiB,CAAA,OAAQ,CAAC;wBACxB,GAAG,IAAI;wBACP,cAAc;+BAAI,KAAK,YAAY;4BAAE;yBAAO;oBAC9C,CAAC;YACH;YAEA,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,aAAa;YACb,MAAM;QACR;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,CAAC,KAAK;YACR,MAAM;YACN;QACF;QAEA,MAAM,eAAe,gBAAgB;QACrC,OAAO,IAAI,CAAC,cAAc;IAC5B;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI;YACF,IAAI,SAAS,GAAG,EAAE;gBAChB,MAAM,eAAe,gBAAgB,SAAS,GAAG;gBACjD,MAAM,OAAO,SAAS,aAAa,CAAC;gBACpC,KAAK,IAAI,GAAG;gBACZ,KAAK,QAAQ,GAAG,SAAS,IAAI,IAAI;gBACjC,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,KAAK,KAAK;gBACV,SAAS,IAAI,CAAC,WAAW,CAAC;YAC5B,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACR;IACF;IAEA,MAAM,eAAe,OAAO,IAAI,eAAe,IAAI;QACjD,IAAI;YACF,IAAI,cAAc,WAAW,cAAc,WAAW;gBACpD,qBAAqB;gBACrB,MAAM,WAAW,cAAc,UAAU,SAAS;gBAClD,MAAM,oBAAoB;gBAE1B,qBAAqB;gBACrB,iBAAiB,CAAA,OAAQ,CAAC;wBACxB,GAAG,IAAI;wBACP,CAAC,UAAU,EAAE,EAAE;oBACjB,CAAC;YACH,OAAO,IAAI,cAAc,kBAAkB,cAAc;gBACvD,mBAAmB;gBACnB,MAAM,kBAAkB,aAAa,QAAQ;gBAE7C,qBAAqB;gBACrB,iBAAiB,CAAA,OAAQ,CAAC;wBACxB,GAAG,IAAI;wBACP,cAAc,KAAK,YAAY,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,QAAQ,KAAK,aAAa,QAAQ;oBACtF,CAAC;YACH,OAAO;gBACL,sCAAsC;gBACtC,iBAAiB,CAAA,OAAQ,CAAC;wBACxB,GAAG,IAAI;wBACP,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;oBACxD,CAAC;YACH;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCACpD,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,8IAAA,CAAA,gBAAa;gCAAC,MAAM;;;;;;;;;;;;;;;;;8BAKzB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,WAAW,CAAC,8BAA8B,EAAE,cAAc,UAAU,6CAA6C,qCAAqC;4BACtJ,SAAS,IAAM,aAAa;sCAC7B;;;;;;sCAGD,8OAAC;4BACC,WAAW,CAAC,8BAA8B,EAAE,cAAc,YAAY,6CAA6C,qCAAqC;4BACxJ,SAAS,IAAM,aAAa;sCAC7B;;;;;;sCAGD,8OAAC;4BACC,WAAW,CAAC,8BAA8B,EAAE,cAAc,iBAAiB,6CAA6C,qCAAqC;4BAC7J,SAAS,IAAM,aAAa;sCAC7B;;;;;;;;;;;;8BAKH,8OAAC;oBAAI,WAAU;8BACZ,aAAa,CAAC,UAAU,CAAC,MAAM,KAAK,kBACnC,8OAAC;wBAAI,WAAU;kCAAiC;;;;;6CAIhD,8OAAC;wBAAI,WAAU;kCACZ,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,UAAU,sBACvC,8OAAC;gCAEC,WAAU;;kDAEV,8OAAC;wCACC,WAAU;wCACV,SAAS,IAAM,mBAAmB,SAAS,GAAG;;0DAE9C,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,8IAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;0DAEvB,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAkC,SAAS,IAAI;;;;;;0EAC7D,8OAAC,8IAAA,CAAA,oBAAiB;gEAAC,WAAU;;;;;;;;;;;;kEAE/B,8OAAC;wDAAE,WAAU;;4DAAwB;4DAAa,SAAS,IAAI;;;;;;;;;;;;;;;;;;;kDAGnE,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDACC,SAAS,IAAM,mBAAmB;gDAClC,WAAU;gDACV,cAAW;gDACX,OAAM;0DAEN,cAAA,8OAAC,8IAAA,CAAA,SAAM;;;;;;;;;;0DAIT,8OAAC;gDACC,SAAS,IAAM,aAAa,SAAS,EAAE,EAAE;gDACzC,WAAU;gDACV,cAAW;gDACX,OAAM;0DAEN,cAAA,8OAAC,8IAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;+BApCP,SAAS,EAAE,IAAI,GAAG,UAAU,CAAC,EAAE,OAAO;;;;;;;;;;;;;;;8BA6CrD,8OAAC;oBAAI,WAAU;;wBAEZ,cAAc,gCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,MAAK;4CACL,KAAI;4CACJ,KAAI;4CACJ,KAAK;4CACL,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAGhB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,MAAK;4CACL,MAAK;4CACL,KAAI;4CACJ,KAAI;4CACJ,KAAK;4CACL,WAAU;4CACV,aAAY;;;;;;;;;;;;;;;;;;sCAMpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;8CAGrC,8OAAC;;sDACC,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,WAAU;4CACV,KAAK;4CACL,UAAU;4CACV,UAAU;;;;;;sDAEZ,8OAAC;4CACC,SAAS,IAAM,aAAa,OAAO,CAAC,KAAK;4CACzC,WAAW,CAAC,gFAAgF,EAAE,YAAY,kCAAkC,IAAI;4CAChJ,UAAU;sDAET,0BACC;;kEACE,8OAAC,8IAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAsB;;6EAG7C;;kEACE,8OAAC,8IAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjD", "debugId": null}}, {"offset": {"line": 2535, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/utils/fileValidation.js"], "sourcesContent": ["// File validation utilities\r\n\r\nexport const FILE_CONSTRAINTS = {\r\n  resume: {\r\n    maxSize: 5 * 1024 * 1024, // 5MB\r\n    allowedTypes: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],\r\n    allowedExtensions: ['.pdf', '.doc', '.docx'],\r\n    displayName: 'Resume'\r\n  },\r\n  profile_image: {\r\n    maxSize: 2 * 1024 * 1024, // 2MB\r\n    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],\r\n    allowedExtensions: ['.jpg', '.jpeg', '.png', '.gif', '.webp'],\r\n    displayName: 'Profile Image'\r\n  },\r\n  certificate: {\r\n    maxSize: 5 * 1024 * 1024, // 5MB\r\n    allowedTypes: ['application/pdf', 'image/jpeg', 'image/png'],\r\n    allowedExtensions: ['.pdf', '.jpg', '.jpeg', '.png'],\r\n    displayName: 'Certificate'\r\n  },\r\n  marksheet: {\r\n    maxSize: 5 * 1024 * 1024, // 5MB\r\n    allowedTypes: ['application/pdf', 'image/jpeg', 'image/png'],\r\n    allowedExtensions: ['.pdf', '.jpg', '.jpeg', '.png'],\r\n    displayName: 'Marksheet'\r\n  },\r\n  cover_letter: {\r\n    maxSize: 2 * 1024 * 1024, // 2MB\r\n    allowedTypes: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],\r\n    allowedExtensions: ['.pdf', '.doc', '.docx'],\r\n    displayName: 'Cover Letter'\r\n  },\r\n  generic_document: {\r\n    maxSize: 10 * 1024 * 1024, // 10MB\r\n    allowedTypes: [\r\n      'application/pdf',\r\n      'application/msword',\r\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\r\n      'image/jpeg',\r\n      'image/png'\r\n    ],\r\n    allowedExtensions: ['.pdf', '.doc', '.docx', '.jpg', '.jpeg', '.png'],\r\n    displayName: 'Document'\r\n  }\r\n};\r\n\r\nexport const validateFile = (file, fileType = 'generic_document') => {\r\n  const constraints = FILE_CONSTRAINTS[fileType];\r\n  const errors = [];\r\n  const warnings = [];\r\n\r\n  if (!file) {\r\n    return {\r\n      isValid: false,\r\n      errors: ['No file selected'],\r\n      warnings: [],\r\n      file: null\r\n    };\r\n  }\r\n\r\n  // Check file size\r\n  if (file.size > constraints.maxSize) {\r\n    const maxSizeMB = Math.round(constraints.maxSize / (1024 * 1024));\r\n    const fileSizeMB = Math.round(file.size / (1024 * 1024) * 10) / 10;\r\n    errors.push(`File size (${fileSizeMB}MB) exceeds maximum allowed size of ${maxSizeMB}MB`);\r\n  }\r\n\r\n  // Check file type\r\n  if (!constraints.allowedTypes.includes(file.type)) {\r\n    const allowedTypesDisplay = constraints.allowedExtensions.join(', ');\r\n    errors.push(`File type not supported. Allowed formats: ${allowedTypesDisplay}`);\r\n  }\r\n\r\n  // Check file extension\r\n  const fileExtension = '.' + file.name.split('.').pop().toLowerCase();\r\n  if (!constraints.allowedExtensions.includes(fileExtension)) {\r\n    const allowedExtensionsDisplay = constraints.allowedExtensions.join(', ');\r\n    errors.push(`File extension not allowed. Supported extensions: ${allowedExtensionsDisplay}`);\r\n  }\r\n\r\n  // Additional validations based on file type\r\n  switch (fileType) {\r\n    case 'resume':\r\n      if (file.size < 50 * 1024) { // Less than 50KB\r\n        warnings.push('Resume file seems very small. Please ensure it contains adequate content.');\r\n      }\r\n      if (file.name.length > 100) {\r\n        warnings.push('File name is very long. Consider using a shorter name.');\r\n      }\r\n      break;\r\n\r\n    case 'profile_image':\r\n      if (file.size < 10 * 1024) { // Less than 10KB\r\n        warnings.push('Image file seems very small. Please ensure it\\'s a clear photo.');\r\n      }\r\n      break;\r\n\r\n    case 'certificate':\r\n    case 'marksheet':\r\n      if (file.size < 100 * 1024) { // Less than 100KB\r\n        warnings.push('Document file seems small. Please ensure it\\'s clearly readable.');\r\n      }\r\n      break;\r\n  }\r\n\r\n  // Check for potentially malicious file names\r\n  if (file.name.includes('..') || file.name.includes('/') || file.name.includes('\\\\')) {\r\n    errors.push('File name contains invalid characters');\r\n  }\r\n\r\n  // Check for very long file names\r\n  if (file.name.length > 255) {\r\n    errors.push('File name is too long (maximum 255 characters)');\r\n  }\r\n\r\n  return {\r\n    isValid: errors.length === 0,\r\n    errors,\r\n    warnings,\r\n    file,\r\n    size: file.size,\r\n    sizeDisplay: formatFileSize(file.size),\r\n    type: file.type,\r\n    name: file.name\r\n  };\r\n};\r\n\r\nexport const validateMultipleFiles = (files, fileType = 'generic_document', maxFiles = 5) => {\r\n  const results = [];\r\n  const overallErrors = [];\r\n\r\n  if (!files || files.length === 0) {\r\n    return {\r\n      isValid: false,\r\n      errors: ['No files selected'],\r\n      warnings: [],\r\n      results: [],\r\n      totalSize: 0\r\n    };\r\n  }\r\n\r\n  if (files.length > maxFiles) {\r\n    overallErrors.push(`Too many files selected. Maximum allowed: ${maxFiles}`);\r\n  }\r\n\r\n  let totalSize = 0;\r\n  let hasErrors = false;\r\n\r\n  for (let i = 0; i < files.length; i++) {\r\n    const validation = validateFile(files[i], fileType);\r\n    results.push({\r\n      index: i,\r\n      fileName: files[i].name,\r\n      ...validation\r\n    });\r\n\r\n    totalSize += files[i].size;\r\n\r\n    if (!validation.isValid) {\r\n      hasErrors = true;\r\n    }\r\n  }\r\n\r\n  // Check total size for multiple files\r\n  const maxTotalSize = FILE_CONSTRAINTS[fileType].maxSize * Math.min(files.length, maxFiles);\r\n  if (totalSize > maxTotalSize) {\r\n    const maxTotalSizeMB = Math.round(maxTotalSize / (1024 * 1024));\r\n    const totalSizeMB = Math.round(totalSize / (1024 * 1024) * 10) / 10;\r\n    overallErrors.push(`Total file size (${totalSizeMB}MB) exceeds maximum allowed (${maxTotalSizeMB}MB)`);\r\n    hasErrors = true;\r\n  }\r\n\r\n  return {\r\n    isValid: !hasErrors && overallErrors.length === 0,\r\n    errors: overallErrors,\r\n    warnings: [],\r\n    results,\r\n    totalSize,\r\n    totalSizeDisplay: formatFileSize(totalSize),\r\n    fileCount: files.length\r\n  };\r\n};\r\n\r\nexport const formatFileSize = (bytes) => {\r\n  if (bytes === 0) return '0 Bytes';\r\n\r\n  const k = 1024;\r\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\r\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n\r\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];\r\n};\r\n\r\nexport const getFileTypeIcon = (fileType) => {\r\n  const iconMap = {\r\n    'application/pdf': 'file-text',\r\n    'application/msword': 'file-text',\r\n    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'file-text',\r\n    'image/jpeg': 'image',\r\n    'image/png': 'image',\r\n    'image/gif': 'image',\r\n    'image/webp': 'image'\r\n  };\r\n\r\n  return iconMap[fileType] || 'file';\r\n};\r\n\r\nexport const createFileUploadPreview = (file, validation) => {\r\n  return {\r\n    name: file.name,\r\n    size: formatFileSize(file.size),\r\n    type: file.type,\r\n    icon: getFileTypeIcon(file.type),\r\n    isValid: validation.isValid,\r\n    errors: validation.errors,\r\n    warnings: validation.warnings,\r\n    preview: file.type.startsWith('image/') ? URL.createObjectURL(file) : null\r\n  };\r\n};\r\n\r\n// Error messages for different scenarios\r\nexport const getFileErrorMessage = (validationResult) => {\r\n  if (validationResult.isValid) {\r\n    return null;\r\n  }\r\n\r\n  const primaryError = validationResult.errors[0];\r\n  \r\n  if (primaryError.includes('size')) {\r\n    return {\r\n      title: 'File Too Large',\r\n      message: primaryError,\r\n      suggestion: 'Please compress the file or choose a smaller file.',\r\n      type: 'size'\r\n    };\r\n  }\r\n\r\n  if (primaryError.includes('type') || primaryError.includes('extension')) {\r\n    return {\r\n      title: 'Unsupported File Format',\r\n      message: primaryError,\r\n      suggestion: 'Please convert your file to a supported format.',\r\n      type: 'format'\r\n    };\r\n  }\r\n\r\n  if (primaryError.includes('name')) {\r\n    return {\r\n      title: 'Invalid File Name',\r\n      message: primaryError,\r\n      suggestion: 'Please rename your file and try again.',\r\n      type: 'name'\r\n    };\r\n  }\r\n\r\n  return {\r\n    title: 'File Upload Error',\r\n    message: primaryError,\r\n    suggestion: 'Please check your file and try again.',\r\n    type: 'generic'\r\n  };\r\n};\r\n\r\nexport default {\r\n  validateFile,\r\n  validateMultipleFiles,\r\n  formatFileSize,\r\n  getFileTypeIcon,\r\n  createFileUploadPreview,\r\n  getFileErrorMessage,\r\n  FILE_CONSTRAINTS\r\n}; "], "names": [], "mappings": "AAAA,4BAA4B;;;;;;;;;;;AAErB,MAAM,mBAAmB;IAC9B,QAAQ;QACN,SAAS,IAAI,OAAO;QACpB,cAAc;YAAC;YAAmB;YAAsB;SAA0E;QAClI,mBAAmB;YAAC;YAAQ;YAAQ;SAAQ;QAC5C,aAAa;IACf;IACA,eAAe;QACb,SAAS,IAAI,OAAO;QACpB,cAAc;YAAC;YAAc;YAAa;YAAa;SAAa;QACpE,mBAAmB;YAAC;YAAQ;YAAS;YAAQ;YAAQ;SAAQ;QAC7D,aAAa;IACf;IACA,aAAa;QACX,SAAS,IAAI,OAAO;QACpB,cAAc;YAAC;YAAmB;YAAc;SAAY;QAC5D,mBAAmB;YAAC;YAAQ;YAAQ;YAAS;SAAO;QACpD,aAAa;IACf;IACA,WAAW;QACT,SAAS,IAAI,OAAO;QACpB,cAAc;YAAC;YAAmB;YAAc;SAAY;QAC5D,mBAAmB;YAAC;YAAQ;YAAQ;YAAS;SAAO;QACpD,aAAa;IACf;IACA,cAAc;QACZ,SAAS,IAAI,OAAO;QACpB,cAAc;YAAC;YAAmB;YAAsB;SAA0E;QAClI,mBAAmB;YAAC;YAAQ;YAAQ;SAAQ;QAC5C,aAAa;IACf;IACA,kBAAkB;QAChB,SAAS,KAAK,OAAO;QACrB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;QACD,mBAAmB;YAAC;YAAQ;YAAQ;YAAS;YAAQ;YAAS;SAAO;QACrE,aAAa;IACf;AACF;AAEO,MAAM,eAAe,CAAC,MAAM,WAAW,kBAAkB;IAC9D,MAAM,cAAc,gBAAgB,CAAC,SAAS;IAC9C,MAAM,SAAS,EAAE;IACjB,MAAM,WAAW,EAAE;IAEnB,IAAI,CAAC,MAAM;QACT,OAAO;YACL,SAAS;YACT,QAAQ;gBAAC;aAAmB;YAC5B,UAAU,EAAE;YACZ,MAAM;QACR;IACF;IAEA,kBAAkB;IAClB,IAAI,KAAK,IAAI,GAAG,YAAY,OAAO,EAAE;QACnC,MAAM,YAAY,KAAK,KAAK,CAAC,YAAY,OAAO,GAAG,CAAC,OAAO,IAAI;QAC/D,MAAM,aAAa,KAAK,KAAK,CAAC,KAAK,IAAI,GAAG,CAAC,OAAO,IAAI,IAAI,MAAM;QAChE,OAAO,IAAI,CAAC,CAAC,WAAW,EAAE,WAAW,oCAAoC,EAAE,UAAU,EAAE,CAAC;IAC1F;IAEA,kBAAkB;IAClB,IAAI,CAAC,YAAY,YAAY,CAAC,QAAQ,CAAC,KAAK,IAAI,GAAG;QACjD,MAAM,sBAAsB,YAAY,iBAAiB,CAAC,IAAI,CAAC;QAC/D,OAAO,IAAI,CAAC,CAAC,0CAA0C,EAAE,qBAAqB;IAChF;IAEA,uBAAuB;IACvB,MAAM,gBAAgB,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,WAAW;IAClE,IAAI,CAAC,YAAY,iBAAiB,CAAC,QAAQ,CAAC,gBAAgB;QAC1D,MAAM,2BAA2B,YAAY,iBAAiB,CAAC,IAAI,CAAC;QACpE,OAAO,IAAI,CAAC,CAAC,kDAAkD,EAAE,0BAA0B;IAC7F;IAEA,4CAA4C;IAC5C,OAAQ;QACN,KAAK;YACH,IAAI,KAAK,IAAI,GAAG,KAAK,MAAM;gBACzB,SAAS,IAAI,CAAC;YAChB;YACA,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,KAAK;gBAC1B,SAAS,IAAI,CAAC;YAChB;YACA;QAEF,KAAK;YACH,IAAI,KAAK,IAAI,GAAG,KAAK,MAAM;gBACzB,SAAS,IAAI,CAAC;YAChB;YACA;QAEF,KAAK;QACL,KAAK;YACH,IAAI,KAAK,IAAI,GAAG,MAAM,MAAM;gBAC1B,SAAS,IAAI,CAAC;YAChB;YACA;IACJ;IAEA,6CAA6C;IAC7C,IAAI,KAAK,IAAI,CAAC,QAAQ,CAAC,SAAS,KAAK,IAAI,CAAC,QAAQ,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,CAAC,OAAO;QACnF,OAAO,IAAI,CAAC;IACd;IAEA,iCAAiC;IACjC,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,KAAK;QAC1B,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;QACA;QACA;QACA,MAAM,KAAK,IAAI;QACf,aAAa,eAAe,KAAK,IAAI;QACrC,MAAM,KAAK,IAAI;QACf,MAAM,KAAK,IAAI;IACjB;AACF;AAEO,MAAM,wBAAwB,CAAC,OAAO,WAAW,kBAAkB,EAAE,WAAW,CAAC;IACtF,MAAM,UAAU,EAAE;IAClB,MAAM,gBAAgB,EAAE;IAExB,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;QAChC,OAAO;YACL,SAAS;YACT,QAAQ;gBAAC;aAAoB;YAC7B,UAAU,EAAE;YACZ,SAAS,EAAE;YACX,WAAW;QACb;IACF;IAEA,IAAI,MAAM,MAAM,GAAG,UAAU;QAC3B,cAAc,IAAI,CAAC,CAAC,0CAA0C,EAAE,UAAU;IAC5E;IAEA,IAAI,YAAY;IAChB,IAAI,YAAY;IAEhB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QACrC,MAAM,aAAa,aAAa,KAAK,CAAC,EAAE,EAAE;QAC1C,QAAQ,IAAI,CAAC;YACX,OAAO;YACP,UAAU,KAAK,CAAC,EAAE,CAAC,IAAI;YACvB,GAAG,UAAU;QACf;QAEA,aAAa,KAAK,CAAC,EAAE,CAAC,IAAI;QAE1B,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,YAAY;QACd;IACF;IAEA,sCAAsC;IACtC,MAAM,eAAe,gBAAgB,CAAC,SAAS,CAAC,OAAO,GAAG,KAAK,GAAG,CAAC,MAAM,MAAM,EAAE;IACjF,IAAI,YAAY,cAAc;QAC5B,MAAM,iBAAiB,KAAK,KAAK,CAAC,eAAe,CAAC,OAAO,IAAI;QAC7D,MAAM,cAAc,KAAK,KAAK,CAAC,YAAY,CAAC,OAAO,IAAI,IAAI,MAAM;QACjE,cAAc,IAAI,CAAC,CAAC,iBAAiB,EAAE,YAAY,6BAA6B,EAAE,eAAe,GAAG,CAAC;QACrG,YAAY;IACd;IAEA,OAAO;QACL,SAAS,CAAC,aAAa,cAAc,MAAM,KAAK;QAChD,QAAQ;QACR,UAAU,EAAE;QACZ;QACA;QACA,kBAAkB,eAAe;QACjC,WAAW,MAAM,MAAM;IACzB;AACF;AAEO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAEO,MAAM,kBAAkB,CAAC;IAC9B,MAAM,UAAU;QACd,mBAAmB;QACnB,sBAAsB;QACtB,2EAA2E;QAC3E,cAAc;QACd,aAAa;QACb,aAAa;QACb,cAAc;IAChB;IAEA,OAAO,OAAO,CAAC,SAAS,IAAI;AAC9B;AAEO,MAAM,0BAA0B,CAAC,MAAM;IAC5C,OAAO;QACL,MAAM,KAAK,IAAI;QACf,MAAM,eAAe,KAAK,IAAI;QAC9B,MAAM,KAAK,IAAI;QACf,MAAM,gBAAgB,KAAK,IAAI;QAC/B,SAAS,WAAW,OAAO;QAC3B,QAAQ,WAAW,MAAM;QACzB,UAAU,WAAW,QAAQ;QAC7B,SAAS,KAAK,IAAI,CAAC,UAAU,CAAC,YAAY,IAAI,eAAe,CAAC,QAAQ;IACxE;AACF;AAGO,MAAM,sBAAsB,CAAC;IAClC,IAAI,iBAAiB,OAAO,EAAE;QAC5B,OAAO;IACT;IAEA,MAAM,eAAe,iBAAiB,MAAM,CAAC,EAAE;IAE/C,IAAI,aAAa,QAAQ,CAAC,SAAS;QACjC,OAAO;YACL,OAAO;YACP,SAAS;YACT,YAAY;YACZ,MAAM;QACR;IACF;IAEA,IAAI,aAAa,QAAQ,CAAC,WAAW,aAAa,QAAQ,CAAC,cAAc;QACvE,OAAO;YACL,OAAO;YACP,SAAS;YACT,YAAY;YACZ,MAAM;QACR;IACF;IAEA,IAAI,aAAa,QAAQ,CAAC,SAAS;QACjC,OAAO;YACL,OAAO;YACP,SAAS;YACT,YAAY;YACZ,MAAM;QACR;IACF;IAEA,OAAO;QACL,OAAO;QACP,SAAS;QACT,YAAY;QACZ,MAAM;IACR;AACF;uCAEe;IACb;IACA;IACA;IACA;IACA;IACA;IACA;AACF", "debugId": null}}, {"offset": {"line": 2850, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/combine/frontend/src/app/profile/page.jsx"], "sourcesContent": ["'use client';\r\nimport { useState, useEffect } from 'react';\r\nimport Image from 'next/image';\r\nimport { FaFileAlt, FaBuilding, FaMapMarkerAlt, FaPhoneAlt, FaUser, FaSpinner } from 'react-icons/fa';\r\nimport ResumeModal from './ResumeModal';\r\nimport DocumentsModal from './DocumentsModal';\r\nimport { studentsAPI } from '../../api/students';\r\nimport { useNotification } from '../../contexts/NotificationContext';\r\nimport { validateFile } from '../../utils/fileValidation';\r\n\r\nexport default function ProfilePage() {\r\n  const { handleApiError, showFileUploadError } = useNotification();\r\n  const [isResumeModalOpen, setIsResumeModalOpen] = useState(false);\r\n  const [isDocumentsModalOpen, setIsDocumentsModalOpen] = useState(false);\r\n  const [profile, setProfile] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [semesterMarksheets, setSemesterMarksheets] = useState([]);\r\n  const [resumeCount, setResumeCount] = useState(0);\r\n  const [lastResumeUpdate, setLastResumeUpdate] = useState(null);\r\n  const [companyStats, setCompanyStats] = useState({\r\n    totalListings: 0,\r\n    eligibleJobs: 0,\r\n    loading: true\r\n  });\r\n  \r\n  useEffect(() => {\r\n    async function fetchProfileData() {\r\n      try {\r\n        setLoading(true);\r\n        const profileData = await studentsAPI.getProfile();\r\n        setProfile(profileData);\r\n        \r\n        // Fetch semester marksheets\r\n        const marksheets = await studentsAPI.getSemesterMarksheets();\r\n        setSemesterMarksheets(marksheets);\r\n        \r\n        // Fetch resume count\r\n        await fetchResumeCount();\r\n        \r\n        // Fetch company stats\r\n        fetchCompanyStats();\r\n        \r\n        setLoading(false);\r\n      } catch (err) {\r\n        console.error('Error fetching profile:', err);\r\n        handleApiError(err, 'loading profile');\r\n        setError('Failed to load profile data. Please try again later.');\r\n        setLoading(false);\r\n      }\r\n    }\r\n    \r\n    fetchProfileData();\r\n  }, []);\r\n  \r\n  // Function to fetch resume count\r\n  const fetchResumeCount = async () => {\r\n    try {\r\n      const resumes = await studentsAPI.getResumes();\r\n      setResumeCount(resumes.length);\r\n      if (resumes.length > 0) {\r\n        // Get the most recent upload date\r\n        const mostRecent = resumes.reduce((latest, resume) => {\r\n          const resumeDate = new Date(resume.uploaded_at || resume.created_at);\r\n          const latestDate = new Date(latest);\r\n          return resumeDate > latestDate ? resume.uploaded_at || resume.created_at : latest;\r\n        }, resumes[0].uploaded_at || resumes[0].created_at);\r\n        setLastResumeUpdate(mostRecent);\r\n      }\r\n    } catch (err) {\r\n      console.error('Error fetching resumes:', err);\r\n      // Fallback to profile resume if available\r\n      if (profile?.resume) {\r\n        setResumeCount(1);\r\n        setLastResumeUpdate(profile.updated_at);\r\n      }\r\n    }\r\n  };\r\n\r\n  // Function to fetch company statistics\r\n  const fetchCompanyStats = async () => {\r\n    try {\r\n      // Import and use API functions from companies.js\r\n      const { getCompanyStats, fetchCompanies } = await import('../../api/companies');\r\n      \r\n      // Get stats if available\r\n      let stats;\r\n      try {\r\n        const statsResponse = await getCompanyStats();\r\n        stats = statsResponse.data || statsResponse;\r\n      } catch (error) {\r\n        console.log('Could not fetch company stats, calculating from companies data');\r\n        // Fetch all companies to get total count\r\n        const companies = await fetchCompanies();\r\n        stats = {\r\n          total: companies.length,\r\n          active_jobs: companies.reduce((sum, company) => sum + (company.totalActiveJobs || 0), 0)\r\n        };\r\n      }\r\n      \r\n      // Calculate eligible jobs based on profile criteria (e.g., CGPA requirements)\r\n      const eligibleJobsCount = calculateEligibleJobs(stats, profile);\r\n      \r\n      setCompanyStats({\r\n        totalListings: stats.total || 0,\r\n        eligibleJobs: eligibleJobsCount,\r\n        loading: false\r\n      });\r\n      \r\n    } catch (error) {\r\n      console.error('Error fetching company stats:', error);\r\n      setCompanyStats({\r\n        totalListings: 0,\r\n        eligibleJobs: 0,\r\n        loading: false\r\n      });\r\n    }\r\n  };\r\n  \r\n  // Helper function to calculate eligible jobs based on profile\r\n  const calculateEligibleJobs = (companies, profile) => {\r\n    if (!companies || !profile) return 0;\r\n    \r\n    // Get user's CGPA for comparison\r\n    const userCgpa = parseFloat(getOverallCGPA());\r\n    \r\n    // Get user's branch/major for matching\r\n    const userBranch = profile.branch;\r\n    \r\n    // Count jobs that match the user's criteria\r\n    let eligibleCount = 0;\r\n    \r\n    // For each company, check eligibility\r\n    companies.forEach(company => {\r\n      // In a real implementation, we would check each job's requirements\r\n      // For now, use a simple heuristic based on company tier\r\n      const companyJobCount = company.totalActiveJobs || 0;\r\n      let eligibilityPercent = 0;\r\n      \r\n      // Very basic eligibility logic (would be replaced with actual requirements)\r\n      if (userCgpa >= 8.5) {\r\n        eligibilityPercent = 0.9; // 90% of jobs eligible for high CGPA students\r\n      } else if (userCgpa >= 7.5) {\r\n        eligibilityPercent = 0.75; // 75% eligible for good CGPA students\r\n      } else if (userCgpa >= 6.5) {\r\n        eligibilityPercent = 0.5; // 50% eligible for average CGPA students\r\n      } else {\r\n        eligibilityPercent = 0.25; // 25% eligible for below average CGPA students\r\n      }\r\n      \r\n      // Add to eligible count\r\n      eligibleCount += Math.floor(companyJobCount * eligibilityPercent);\r\n    });\r\n    \r\n    return eligibleCount;\r\n  };\r\n\r\n  // Function to handle profile image upload\r\n  const handleProfileImageUpload = async (file) => {\r\n    // Validate file first\r\n    const validation = validateFile(file, 'profile_image');\r\n    if (!validation.isValid) {\r\n      showFileUploadError(validation.errors);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      await studentsAPI.uploadProfileImage(file);\r\n      // Refresh profile data\r\n      const profileData = await studentsAPI.getProfile();\r\n      setProfile(profileData);\r\n    } catch (err) {\r\n      console.error('Error uploading profile image:', err);\r\n      showFileUploadError([\r\n        'Failed to upload profile image',\r\n        'Please check the file format and size',\r\n        'Supported formats: JPG, PNG, GIF (max 2MB)'\r\n      ]);\r\n    }\r\n  };\r\n\r\n  // Function to handle resume upload\r\n  const handleResumeUpload = async (file) => {\r\n    // Validate file first\r\n    const validation = validateFile(file, 'resume');\r\n    if (!validation.isValid) {\r\n      showFileUploadError(validation.errors);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Use the new resume upload API\r\n      await studentsAPI.uploadResume(file, file.name, false);\r\n      // Refresh resume count after upload\r\n      await fetchResumeCount();\r\n    } catch (err) {\r\n      console.error('Error uploading resume:', err);\r\n      showFileUploadError([\r\n        'Failed to upload resume',\r\n        'Please check the file format and size',\r\n        'Supported formats: PDF, DOC, DOCX (max 5MB)'\r\n      ]);\r\n    }\r\n  };\r\n\r\n  // Function to handle resume delete\r\n  const handleResumeDelete = async (resume) => {\r\n    try {\r\n      // Clear any cached resume data\r\n      if (typeof window !== 'undefined' && resume?.id) {\r\n        localStorage.removeItem(`resume_${resume.id}`);\r\n        localStorage.removeItem('resume_count');\r\n        localStorage.removeItem('last_resume_update');\r\n      }\r\n      \r\n      // Force refresh resume count\r\n      await fetchResumeCount();\r\n      \r\n      // If we were displaying a specific resume that was deleted, clear it\r\n      if (resume?.url === profile?.resume) {\r\n        const updatedProfile = { ...profile, resume: null };\r\n        setProfile(updatedProfile);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error handling resume deletion:', error);\r\n    }\r\n  };\r\n\r\n  // Get overall CGPA from database\r\n  const getOverallCGPA = () => {\r\n    return profile?.gpa || '0.00';\r\n  };\r\n  \r\n  // Calculate percentage from CGPA (approximation)\r\n  const calculatePercentage = (cgpa) => {\r\n    if (!cgpa || cgpa === '-') return '-';\r\n    return (parseFloat(cgpa) * 9.5).toFixed(2) + '%';\r\n  };\r\n\r\n  // Helper function to check if a document actually exists\r\n  const isValidDocument = (document) => {\r\n    return document &&\r\n           typeof document === 'string' &&\r\n           document.trim() !== '' &&\r\n           document !== 'null' &&\r\n           document !== 'undefined';\r\n  };\r\n\r\n  // Calculate actual document count\r\n  const getDocumentCount = () => {\r\n    const tenthCount = isValidDocument(profile?.tenth_certificate) ? 1 : 0;\r\n    const twelfthCount = isValidDocument(profile?.twelfth_certificate) ? 1 : 0;\r\n    const semesterCount = semesterMarksheets ?\r\n      semesterMarksheets.filter(sheet => sheet && isValidDocument(sheet.marksheet_url)).length : 0;\r\n\r\n    return tenthCount + twelfthCount + semesterCount;\r\n  };\r\n  \r\n  // Get semester CGPA value\r\n  const getSemesterCGPA = (semNumber) => {\r\n    if (!profile) return '-';\r\n    const semesterCGPA = profile[`semester${semNumber}_cgpa`];\r\n    return semesterCGPA || '-';\r\n  };\r\n  \r\n  // Get semester marksheets sorted by semester number\r\n  const getSortedSemesterMarksheets = () => {\r\n    if (!semesterMarksheets) return [];\r\n    return [...semesterMarksheets].sort((a, b) => a.semester - b.semester);\r\n  };\r\n  \r\n  // Format date to display period (e.g., \"Sep 2021 - Aug 2025\")\r\n  const formatEducationPeriod = (startYear, endYear) => {\r\n    if (!startYear || !endYear) return '-';\r\n    return `${startYear} - ${endYear}`;\r\n  };\r\n  \r\n  // Function to display either the profile image or a fallback with initial\r\n  const renderProfileImage = () => {\r\n    if (loading) {\r\n      return (\r\n        <div className=\"w-50 h-50 bg-blue-100 flex items-center justify-center rounded-lg mb-4\">\r\n          <FaSpinner className=\"animate-spin text-blue-500 text-2xl\" />\r\n        </div>\r\n      );\r\n    }\r\n    \r\n    if (profile?.profile_image_url) {\r\n      return (\r\n        <div className=\"w-50 h-50 bg-blue-100 object-center text-center rounded-lg mb-4 relative mx-auto\">\r\n          <Image \r\n            src={profile.profile_image_url} \r\n            alt={`${profile.first_name} ${profile.last_name}`} \r\n            fill \r\n            className=\"rounded-lg object-cover\"\r\n          />\r\n        </div>\r\n      );\r\n    }\r\n    \r\n    // Fallback to initial\r\n    return (\r\n      <div className=\"w-50 h-50 bg-blue-500 text-white flex items-center justify-center rounded-lg mb-4 mx-auto\">\r\n        <span className=\"text-3xl font-bold\">\r\n          {profile?.initial || (profile?.first_name ? profile.first_name[0].toUpperCase() : 'S')}\r\n        </span>\r\n      </div>\r\n    );\r\n  };\r\n  \r\n  // Display loading state\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex items-center justify-center min-h-screen bg-gray-50\">\r\n        <FaSpinner className=\"animate-spin text-blue-500 text-4xl mr-3\" />\r\n        <span className=\"text-xl text-gray-700\">Loading profile...</span>\r\n      </div>\r\n    );\r\n  }\r\n  \r\n  // Display error state\r\n  if (error) {\r\n    return (\r\n      <div className=\"flex items-center justify-center min-h-screen bg-gray-50\">\r\n        <div className=\"text-center\">\r\n          <p className=\"text-red-500 text-xl mb-4\">{error}</p>\r\n          <button \r\n            onClick={() => window.location.reload()} \r\n            className=\"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600\"\r\n          >\r\n            Retry\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"bg-gray-50 min-h-screen p-6\">\r\n      <div className=\"max-w-8xl mx-auto grid grid-cols-1 lg:grid-cols-12 gap-6\">\r\n        {/* Left Column - Student Information (Smaller) */}\r\n        <div className=\"lg:col-span-3 bg-white rounded-lg p-5 shadow-sm h-fit content-card profile-container\">\r\n          <div className=\"flex justify-between\">\r\n            {renderProfileImage()}\r\n          </div>\r\n          \r\n          <h1 className=\"text-xl font-bold text-center mt-2 text-gray-800\">\r\n            {profile?.first_name && profile?.last_name \r\n              ? `${profile.first_name} ${profile.last_name}` \r\n              : '-'}\r\n          </h1>\r\n          \r\n          <div className=\"mt-4 space-y-3 text-md\">\r\n            <div className=\"flex\">\r\n              <p className=\"text-gray-500 w-20\">Student ID</p>\r\n              <p className=\"font-medium text-gray-800\">: {profile?.student_id || '-'}</p>\r\n            </div>\r\n            <div className=\"flex\">\r\n              <p className=\"text-gray-500 w-20\">Major</p>\r\n              <p className=\"font-medium text-gray-800\">: {profile?.branch || '-'}</p>\r\n            </div>\r\n            <div className=\"flex\">\r\n              <p className=\"text-gray-500 w-20\">Passed Out</p>\r\n              <p className=\"font-medium text-gray-800\">: {profile?.passout_year || '-'}</p>\r\n            </div>\r\n            <div className=\"flex\">\r\n              <p className=\"text-gray-500 w-20\">Gender</p>\r\n              <p className=\"font-medium text-gray-800\">: {profile?.gender || '-'}</p>\r\n            </div>\r\n            <div className=\"flex\">\r\n              <p className=\"text-gray-500 w-20\">Birthday</p>\r\n              <p className=\"font-medium text-gray-800\">: {profile?.date_of_birth || '-'}</p>\r\n            </div>\r\n            <div className=\"flex\">\r\n              <p className=\"text-gray-500 w-20\">Phone</p>\r\n              <p className=\"font-medium text-gray-800\">: {profile?.phone || '-'}</p>\r\n            </div>\r\n            <div className=\"flex\">\r\n              <p className=\"text-gray-500 w-20\">Email</p>\r\n              <p className=\"font-medium text-gray-800\">: {profile?.contact_email || profile?.user?.email || '-'}</p>\r\n            </div>\r\n            <div className=\"flex\">\r\n              <p className=\"text-gray-500 w-20\">Campus</p>\r\n              <p className=\"font-medium text-gray-800\">: {profile?.college_name || '-'}</p>\r\n            </div>\r\n            <div className=\"flex\">\r\n              <p className=\"text-gray-500 w-20\">Placement</p>\r\n              <p className=\"font-medium text-gray-800\">: {profile?.joining_year && profile?.passout_year \r\n                ? `${profile.joining_year}-${profile.passout_year}` \r\n                : '-'}\r\n              </p>\r\n            </div>\r\n          </div>\r\n          {/* Skills Section */}\r\n          <div className=\"mt-6\">\r\n            <h3 className=\"text-lg font-semibold mb-3 text-gray-800\">Skills</h3>\r\n            <div className=\"flex flex-wrap gap-2\">\r\n              {(() => {\r\n                const skills = profile?.skills || selectedStudent?.skills;\r\n\r\n                if (Array.isArray(skills) && skills.length > 0) {\r\n                  return skills.map((skill, index) => (\r\n                    <span \r\n                      key={index} \r\n                      className=\"bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm\"\r\n                    >\r\n                      {skill}\r\n                    </span>\r\n                  ));\r\n                } else if (typeof skills === 'string' && skills.trim()) {\r\n                  return skills.split(',').map((skill, index) => (\r\n                    <span \r\n                      key={index} \r\n                      className=\"bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm\"\r\n                    >\r\n                      {skill.trim()}\r\n                    </span>\r\n                  ));\r\n                } else {\r\n                  return <p className=\"text-gray-600\">No skills listed</p>;\r\n                }\r\n              })()}\r\n            </div>\r\n        \r\n          </div>\r\n        </div>\r\n\r\n        {/* Middle Column - Academic Details (Expanded) */}\r\n        <div className=\"lg:col-span-6 space-y-6\">\r\n          {/* Combined Academic Details */}\r\n          <div className=\"bg-white rounded-lg p-5 shadow-sm content-card\">\r\n          <h2 className=\"text-xl font-semibold mb-6 text-gray-800\">Academic</h2>\r\n            <div className=\"flex justify-between items-center mb-3\">\r\n                <div className=\"flex items-center\">\r\n                  <h3 className=\"text-lg font-semibold text-gray-800\">Semester Wise score</h3>\r\n                  <div className=\"bg-blue-50 inline-flex items-center px-3 py-1 rounded-full ml-3\">\r\n                    <span className=\"text-blue-600 font-medium\">{getOverallCGPA()}</span>\r\n                    <span className=\"text-sm text-gray-500 ml-1\">CGPA</span>\r\n                    <span className=\"text-blue-600 ml-2\">{calculatePercentage(getOverallCGPA())}</span>\r\n                  </div>\r\n                </div>\r\n                <div className=\"text-lg font-semibold text-gray-800\">\r\n                  {formatEducationPeriod(profile?.joining_year, profile?.passout_year)}\r\n                </div>\r\n              </div>\r\n            {/* Current Semester Scores */}\r\n            <div className=\"mb-6\">\r\n              <div className=\"overflow-x-auto\">\r\n                <table className=\"w-full border-collapse border border-gray-300\">\r\n                  <thead>\r\n                    <tr className=\"bg-gray-50\">\r\n                      <th className=\"border border-gray-300 px-4 py-3 text-sm font-semibold text-gray-700\">Sem</th>\r\n                      {[1, 2, 3, 4, 5, 6, 7, 8].map(sem => (\r\n                        <th key={sem} className=\"border border-gray-300 px-4 py-3 text-sm font-semibold text-gray-700\">{sem}</th>\r\n                      ))}\r\n                    </tr>\r\n                  </thead>\r\n                  <tbody>\r\n                    <tr>\r\n                      <td className=\"border border-gray-300 px-4 py-3 text-sm font-medium text-gray-700\">Cgpa</td>\r\n                      {[1, 2, 3, 4, 5, 6, 7, 8].map(sem => (\r\n                        <td key={sem} className=\"border border-gray-300 px-4 py-3 text-sm text-gray-700\">\r\n                          {getSemesterCGPA(sem)}\r\n                        </td>\r\n                      ))}\r\n                    </tr>\r\n                  </tbody>\r\n                </table>\r\n              </div>\r\n            </div>\r\n            <hr className=\"my-6\" />\r\n\r\n            {/* Class XII */}\r\n            <div className=\"mb-6\">\r\n              <div className=\"flex justify-between items-center mb-3\">\r\n                <div className=\"flex items-center\">\r\n                  <h3 className=\"text-lg font-semibold text-gray-800\">Class XII</h3>\r\n                  <div className=\"bg-blue-50 inline-flex items-center px-3 py-1 rounded-full ml-3\">\r\n                    <span className=\"text-blue-600 font-medium\">{profile?.twelfth_cgpa || '-'}</span>\r\n                    <span className=\"text-sm text-gray-500 ml-1\">CGPA</span>\r\n                    <span className=\"text-blue-600 ml-2\">{profile?.twelfth_percentage || '-'}</span>\r\n                  </div>\r\n                </div>\r\n                <div className=\"text-lg font-semibold text-gray-800\">\r\n                  {profile?.twelfth_year_of_passing \r\n                    ? `${parseInt(profile.twelfth_year_of_passing) - 2} - ${profile.twelfth_year_of_passing}` \r\n                    : '-'}\r\n                </div>\r\n              </div>\r\n              <div className=\"flex justify-between items-start mb-2\">\r\n                <div className=\"grid grid-cols-2 gap-6 w-full\">\r\n                  <div className=\"space-y-3\">\r\n                    <div className=\"flex\">\r\n                      <p className=\"text-gray-500 text-sm w-[120px]\">College :</p>\r\n                      <p className=\"text-gray-700 font-medium\">{profile?.twelfth_school || '-'}</p>\r\n                    </div>\r\n                    <div className=\"flex\">\r\n                      <p className=\"text-gray-500 text-sm w-[120px]\">Board :</p>\r\n                      <p className=\"text-gray-700 font-medium\">{profile?.twelfth_board || '-'}</p>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"space-y-3\">\r\n                    <div className=\"flex\">\r\n                      <p className=\"text-gray-500 text-sm w-[120px]\">Location :</p>\r\n                      <p className=\"text-gray-700 font-medium\">{profile?.city || '-'}</p>\r\n                    </div>\r\n                    <div className=\"flex\">\r\n                      <p className=\"text-gray-500 text-sm w-[120px]\">Specialization :</p>\r\n                      <p className=\"text-gray-700 font-medium\">{profile?.twelfth_specialization || '-'}</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n              \r\n             \r\n              \r\n              \r\n\r\n            <hr className=\"my-6\" />\r\n\r\n            {/* Class X */}\r\n            <div>\r\n              <div className=\"flex justify-between items-center mb-3\">\r\n                <div className=\"flex items-center\">\r\n                  <h3 className=\"text-lg font-semibold text-gray-800\">Class X</h3>\r\n                  <div className=\"bg-blue-50 inline-flex items-center px-3 py-1 rounded-full ml-3\">\r\n                    <span className=\"text-blue-600 font-medium\">{profile?.tenth_cgpa || '-'}</span>\r\n                    <span className=\"text-sm text-gray-500 ml-1\">CGPA</span>\r\n                    <span className=\"text-blue-600 ml-2\">{profile?.tenth_percentage || '-'}</span>\r\n                  </div>\r\n                </div>\r\n                <div className=\"text-lg font-semibold text-gray-800\">\r\n                  {profile?.tenth_year_of_passing \r\n                    ? `${parseInt(profile.tenth_year_of_passing) - 1} - ${profile.tenth_year_of_passing}` \r\n                    : '-'}\r\n                </div>\r\n              </div>\r\n              \r\n              <div className=\"flex justify-between items-start mb-2\">\r\n                <div className=\"grid grid-cols-2 gap-6 w-full\">\r\n                  <div className=\"space-y-3\">\r\n                    <div className=\"flex\">\r\n                      <p className=\"text-gray-500 text-sm w-[120px]\">School :</p>\r\n                      <p className=\"text-gray-700 font-medium\">{profile?.tenth_school || '-'}</p>\r\n                    </div>\r\n                    <div className=\"flex\">\r\n                      <p className=\"text-gray-500 text-sm w-[120px]\">Board :</p>\r\n                      <p className=\"text-gray-700 font-medium\">{profile?.tenth_board || '-'}</p>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"space-y-3\">\r\n                    <div className=\"flex\">\r\n                      <p className=\"text-gray-500 text-sm w-[120px]\">Location :</p>\r\n                      <p className=\"text-gray-700 font-medium\">{profile?.city || '-'}</p>\r\n                    </div>\r\n                    <div className=\"flex\">\r\n                      <p className=\"text-gray-500 text-sm w-[120px]\">Specialization :</p>\r\n                      <p className=\"text-gray-700 font-medium\">-</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          {/* Companies Section */}\r\n          <div className=\"bg-white rounded-lg p-5 shadow-sm content-card\">\r\n            <h2 className=\"text-xl font-semibold mb-4 text-gray-800\">Companies</h2>\r\n              \r\n            {companyStats.loading ? (\r\n              <div className=\"flex items-center justify-center py-4\">\r\n                <FaSpinner className=\"animate-spin text-blue-500 text-xl mr-2\" />\r\n                <span className=\"text-gray-600\">Loading company data...</span>\r\n              </div>\r\n            ) : (\r\n              <>\r\n                {/* Total Listings */}\r\n                <div className=\"mb-4\">\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <p className=\"text-gray-500 text-sm\">Total Listings</p>\r\n                    <p className=\"text-lg font-semibold text-gray-700\">{companyStats.totalListings}</p>\r\n                  </div>\r\n                </div>\r\n                  \r\n                {/* Eligible Jobs */}\r\n                <div className=\"mb-4\">\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <p className=\"text-gray-500 text-sm\">Eligible Jobs</p>\r\n                    <p className=\"text-lg font-semibold text-gray-700\">{companyStats.eligibleJobs}</p>\r\n                  </div>\r\n                </div>\r\n              </>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Right Column - Stats and Files */}\r\n        <div className=\"lg:col-span-3 space-y-6\">\r\n          {/* Files Section */}\r\n          <div className=\"bg-white rounded-lg p-5 shadow-sm content-card\">\r\n            <h2 className=\"text-xl font-semibold mb-4 text-gray-800\">My Files</h2>\r\n            \r\n            <div \r\n              className=\"flex items-center p-2 mb-3 cursor-pointer hover:bg-gray-50 rounded-lg transition-colors\"\r\n              onClick={() => setIsResumeModalOpen(true)}\r\n            >\r\n              <div className=\"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-3\">\r\n                <div className=\"text-blue-600\">\r\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z\" />\r\n                  </svg>\r\n                </div>\r\n              </div>\r\n              <div className=\"flex-grow\">\r\n                <h3 className=\"font-medium text-gray-700\">Resumes</h3>\r\n                <p className=\"text-sm text-gray-500\">\r\n                  {resumeCount > 0 \r\n                    ? `${resumeCount} resume${resumeCount > 1 ? 's' : ''} uploaded` + \r\n                      (lastResumeUpdate ? ` • Last updated ${new Date(lastResumeUpdate).toLocaleDateString()}` : '')\r\n                    : 'No resumes uploaded'\r\n                  }\r\n                </p>\r\n              </div>\r\n              <div className=\"bg-green-50 px-3 py-1 rounded-full\">\r\n                <span className=\"text-green-600 font-medium\">{resumeCount}</span>\r\n              </div>\r\n            </div>\r\n            \r\n            <div \r\n              className=\"flex items-center p-2 cursor-pointer hover:bg-gray-50 rounded-lg transition-colors\"\r\n              onClick={() => setIsDocumentsModalOpen(true)}\r\n            >\r\n              <div className=\"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-3\">\r\n                <div className=\"text-blue-600\">\r\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\r\n                  </svg>\r\n                </div>\r\n              </div>\r\n              <div className=\"flex-grow\">\r\n                <h3 className=\"font-medium text-gray-700\">Documents</h3>\r\n                <p className=\"text-sm text-gray-500\">Academic certificates and marksheets</p>\r\n              </div>\r\n              <div className=\"bg-green-50 px-3 py-1 rounded-full\">\r\n                <span className=\"text-green-600 font-medium\">\r\n                  {getDocumentCount()}\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          {/* Address Section */}\r\n          <div className=\"bg-white rounded-lg p-5 shadow-sm content-card\">\r\n            <div className=\"flex justify-between items-center mb-4\">\r\n              <h2 className=\"text-xl font-semibold text-gray-800\">CURRENT ADDRESS</h2>\r\n            </div>\r\n            \r\n            <div className=\"space-y-3 text-sm\">\r\n              <div className=\"flex\">\r\n                <p className=\"text-gray-500 w-20\">City</p>\r\n                <p className=\"font-medium text-gray-700\">: {profile?.city || '-'}</p>\r\n              </div>\r\n              <div className=\"flex\">\r\n                <p className=\"text-gray-500 w-20\">District</p>\r\n                <p className=\"font-medium text-gray-700\">: {profile?.district || '-'}</p>\r\n              </div>\r\n              <div className=\"flex\">\r\n                <p className=\"text-gray-500 w-20\">State</p>\r\n                <p className=\"font-medium text-gray-700\">: {profile?.state || '-'}</p>\r\n              </div>\r\n              <div className=\"flex\">\r\n                <p className=\"text-gray-500 w-20\">Pin Code</p>\r\n                <p className=\"font-medium text-gray-700\">: {profile?.pincode || '-'}</p>\r\n              </div>\r\n              <div className=\"flex\">\r\n                <p className=\"text-gray-500 w-20\">Country</p>\r\n                <p className=\"font-medium text-gray-700\">: {profile?.country || '-'}</p>\r\n              </div>\r\n              <div className=\"flex\">\r\n                <p className=\"text-gray-500 w-20\">Address</p>\r\n                <p className=\"font-medium text-gray-700\">: {profile?.address || '-'}</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      {/* Resume Modal */}\r\n      <ResumeModal \r\n        isOpen={isResumeModalOpen} \r\n        onClose={() => setIsResumeModalOpen(false)}\r\n        resume={profile?.resume_url || profile?.resume}\r\n        onUpload={handleResumeUpload}\r\n        onDelete={handleResumeDelete}\r\n      />\r\n\r\n      {/* Documents Modal */}\r\n      <DocumentsModal\r\n        isOpen={isDocumentsModalOpen}\r\n        onClose={() => setIsDocumentsModalOpen(false)}\r\n        documents={{\r\n          tenth: profile?.tenth_certificate_url || profile?.tenth_certificate,\r\n          twelfth: profile?.twelfth_certificate_url || profile?.twelfth_certificate,\r\n          semesterMarksheets: semesterMarksheets\r\n        }}\r\n        onUploadCertificate={studentsAPI.uploadCertificate}\r\n        onUploadMarksheet={studentsAPI.uploadSemesterMarksheet}\r\n        onDeleteCertificate={studentsAPI.deleteCertificate}\r\n        onDeleteMarksheet={studentsAPI.deleteMarksheet}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\n\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;;AAUe,SAAS;IACtB,MAAM,EAAE,cAAc,EAAE,mBAAmB,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD;IAC9D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC/D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC/C,eAAe;QACf,cAAc;QACd,SAAS;IACX;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,eAAe;YACb,IAAI;gBACF,WAAW;gBACX,MAAM,cAAc,MAAM,sHAAA,CAAA,cAAW,CAAC,UAAU;gBAChD,WAAW;gBAEX,4BAA4B;gBAC5B,MAAM,aAAa,MAAM,sHAAA,CAAA,cAAW,CAAC,qBAAqB;gBAC1D,sBAAsB;gBAEtB,qBAAqB;gBACrB,MAAM;gBAEN,sBAAsB;gBACtB;gBAEA,WAAW;YACb,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,eAAe,KAAK;gBACpB,SAAS;gBACT,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,iCAAiC;IACjC,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,UAAU,MAAM,sHAAA,CAAA,cAAW,CAAC,UAAU;YAC5C,eAAe,QAAQ,MAAM;YAC7B,IAAI,QAAQ,MAAM,GAAG,GAAG;gBACtB,kCAAkC;gBAClC,MAAM,aAAa,QAAQ,MAAM,CAAC,CAAC,QAAQ;oBACzC,MAAM,aAAa,IAAI,KAAK,OAAO,WAAW,IAAI,OAAO,UAAU;oBACnE,MAAM,aAAa,IAAI,KAAK;oBAC5B,OAAO,aAAa,aAAa,OAAO,WAAW,IAAI,OAAO,UAAU,GAAG;gBAC7E,GAAG,OAAO,CAAC,EAAE,CAAC,WAAW,IAAI,OAAO,CAAC,EAAE,CAAC,UAAU;gBAClD,oBAAoB;YACtB;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,2BAA2B;YACzC,0CAA0C;YAC1C,IAAI,SAAS,QAAQ;gBACnB,eAAe;gBACf,oBAAoB,QAAQ,UAAU;YACxC;QACF;IACF;IAEA,uCAAuC;IACvC,MAAM,oBAAoB;QACxB,IAAI;YACF,iDAAiD;YACjD,MAAM,EAAE,eAAe,EAAE,cAAc,EAAE,GAAG;YAE5C,yBAAyB;YACzB,IAAI;YACJ,IAAI;gBACF,MAAM,gBAAgB,MAAM;gBAC5B,QAAQ,cAAc,IAAI,IAAI;YAChC,EAAE,OAAO,OAAO;gBACd,QAAQ,GAAG,CAAC;gBACZ,yCAAyC;gBACzC,MAAM,YAAY,MAAM;gBACxB,QAAQ;oBACN,OAAO,UAAU,MAAM;oBACvB,aAAa,UAAU,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,CAAC,QAAQ,eAAe,IAAI,CAAC,GAAG;gBACxF;YACF;YAEA,8EAA8E;YAC9E,MAAM,oBAAoB,sBAAsB,OAAO;YAEvD,gBAAgB;gBACd,eAAe,MAAM,KAAK,IAAI;gBAC9B,cAAc;gBACd,SAAS;YACX;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,gBAAgB;gBACd,eAAe;gBACf,cAAc;gBACd,SAAS;YACX;QACF;IACF;IAEA,8DAA8D;IAC9D,MAAM,wBAAwB,CAAC,WAAW;QACxC,IAAI,CAAC,aAAa,CAAC,SAAS,OAAO;QAEnC,iCAAiC;QACjC,MAAM,WAAW,WAAW;QAE5B,uCAAuC;QACvC,MAAM,aAAa,QAAQ,MAAM;QAEjC,4CAA4C;QAC5C,IAAI,gBAAgB;QAEpB,sCAAsC;QACtC,UAAU,OAAO,CAAC,CAAA;YAChB,mEAAmE;YACnE,wDAAwD;YACxD,MAAM,kBAAkB,QAAQ,eAAe,IAAI;YACnD,IAAI,qBAAqB;YAEzB,4EAA4E;YAC5E,IAAI,YAAY,KAAK;gBACnB,qBAAqB,KAAK,8CAA8C;YAC1E,OAAO,IAAI,YAAY,KAAK;gBAC1B,qBAAqB,MAAM,sCAAsC;YACnE,OAAO,IAAI,YAAY,KAAK;gBAC1B,qBAAqB,KAAK,yCAAyC;YACrE,OAAO;gBACL,qBAAqB,MAAM,+CAA+C;YAC5E;YAEA,wBAAwB;YACxB,iBAAiB,KAAK,KAAK,CAAC,kBAAkB;QAChD;QAEA,OAAO;IACT;IAEA,0CAA0C;IAC1C,MAAM,2BAA2B,OAAO;QACtC,sBAAsB;QACtB,MAAM,aAAa,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD,EAAE,MAAM;QACtC,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,oBAAoB,WAAW,MAAM;YACrC;QACF;QAEA,IAAI;YACF,MAAM,sHAAA,CAAA,cAAW,CAAC,kBAAkB,CAAC;YACrC,uBAAuB;YACvB,MAAM,cAAc,MAAM,sHAAA,CAAA,cAAW,CAAC,UAAU;YAChD,WAAW;QACb,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,kCAAkC;YAChD,oBAAoB;gBAClB;gBACA;gBACA;aACD;QACH;IACF;IAEA,mCAAmC;IACnC,MAAM,qBAAqB,OAAO;QAChC,sBAAsB;QACtB,MAAM,aAAa,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD,EAAE,MAAM;QACtC,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,oBAAoB,WAAW,MAAM;YACrC;QACF;QAEA,IAAI;YACF,gCAAgC;YAChC,MAAM,sHAAA,CAAA,cAAW,CAAC,YAAY,CAAC,MAAM,KAAK,IAAI,EAAE;YAChD,oCAAoC;YACpC,MAAM;QACR,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,2BAA2B;YACzC,oBAAoB;gBAClB;gBACA;gBACA;aACD;QACH;IACF;IAEA,mCAAmC;IACnC,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,+BAA+B;YAC/B,IAAI,gBAAkB,eAAe,QAAQ,IAAI;;YAIjD;YAEA,6BAA6B;YAC7B,MAAM;YAEN,qEAAqE;YACrE,IAAI,QAAQ,QAAQ,SAAS,QAAQ;gBACnC,MAAM,iBAAiB;oBAAE,GAAG,OAAO;oBAAE,QAAQ;gBAAK;gBAClD,WAAW;YACb;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD;IACF;IAEA,iCAAiC;IACjC,MAAM,iBAAiB;QACrB,OAAO,SAAS,OAAO;IACzB;IAEA,iDAAiD;IACjD,MAAM,sBAAsB,CAAC;QAC3B,IAAI,CAAC,QAAQ,SAAS,KAAK,OAAO;QAClC,OAAO,CAAC,WAAW,QAAQ,GAAG,EAAE,OAAO,CAAC,KAAK;IAC/C;IAEA,yDAAyD;IACzD,MAAM,kBAAkB,CAAC;QACvB,OAAO,YACA,OAAO,aAAa,YACpB,SAAS,IAAI,OAAO,MACpB,aAAa,UACb,aAAa;IACtB;IAEA,kCAAkC;IAClC,MAAM,mBAAmB;QACvB,MAAM,aAAa,gBAAgB,SAAS,qBAAqB,IAAI;QACrE,MAAM,eAAe,gBAAgB,SAAS,uBAAuB,IAAI;QACzE,MAAM,gBAAgB,qBACpB,mBAAmB,MAAM,CAAC,CAAA,QAAS,SAAS,gBAAgB,MAAM,aAAa,GAAG,MAAM,GAAG;QAE7F,OAAO,aAAa,eAAe;IACrC;IAEA,0BAA0B;IAC1B,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,SAAS,OAAO;QACrB,MAAM,eAAe,OAAO,CAAC,CAAC,QAAQ,EAAE,UAAU,KAAK,CAAC,CAAC;QACzD,OAAO,gBAAgB;IACzB;IAEA,oDAAoD;IACpD,MAAM,8BAA8B;QAClC,IAAI,CAAC,oBAAoB,OAAO,EAAE;QAClC,OAAO;eAAI;SAAmB,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;IACvE;IAEA,8DAA8D;IAC9D,MAAM,wBAAwB,CAAC,WAAW;QACxC,IAAI,CAAC,aAAa,CAAC,SAAS,OAAO;QACnC,OAAO,GAAG,UAAU,GAAG,EAAE,SAAS;IACpC;IAEA,0EAA0E;IAC1E,MAAM,qBAAqB;QACzB,IAAI,SAAS;YACX,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,8IAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;;;;;;QAG3B;QAEA,IAAI,SAAS,mBAAmB;YAC9B,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oBACJ,KAAK,QAAQ,iBAAiB;oBAC9B,KAAK,GAAG,QAAQ,UAAU,CAAC,CAAC,EAAE,QAAQ,SAAS,EAAE;oBACjD,IAAI;oBACJ,WAAU;;;;;;;;;;;QAIlB;QAEA,sBAAsB;QACtB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAK,WAAU;0BACb,SAAS,WAAW,CAAC,SAAS,aAAa,QAAQ,UAAU,CAAC,EAAE,CAAC,WAAW,KAAK,GAAG;;;;;;;;;;;IAI7F;IAEA,wBAAwB;IACxB,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,8IAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;8BACrB,8OAAC;oBAAK,WAAU;8BAAwB;;;;;;;;;;;;IAG9C;IAEA,sBAAsB;IACtB,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAC1C,8OAAC;wBACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;wBACrC,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ;;;;;;0CAGH,8OAAC;gCAAG,WAAU;0CACX,SAAS,cAAc,SAAS,YAC7B,GAAG,QAAQ,UAAU,CAAC,CAAC,EAAE,QAAQ,SAAS,EAAE,GAC5C;;;;;;0CAGN,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAClC,8OAAC;gDAAE,WAAU;;oDAA4B;oDAAG,SAAS,cAAc;;;;;;;;;;;;;kDAErE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAClC,8OAAC;gDAAE,WAAU;;oDAA4B;oDAAG,SAAS,UAAU;;;;;;;;;;;;;kDAEjE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAClC,8OAAC;gDAAE,WAAU;;oDAA4B;oDAAG,SAAS,gBAAgB;;;;;;;;;;;;;kDAEvE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAClC,8OAAC;gDAAE,WAAU;;oDAA4B;oDAAG,SAAS,UAAU;;;;;;;;;;;;;kDAEjE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAClC,8OAAC;gDAAE,WAAU;;oDAA4B;oDAAG,SAAS,iBAAiB;;;;;;;;;;;;;kDAExE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAClC,8OAAC;gDAAE,WAAU;;oDAA4B;oDAAG,SAAS,SAAS;;;;;;;;;;;;;kDAEhE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAClC,8OAAC;gDAAE,WAAU;;oDAA4B;oDAAG,SAAS,iBAAiB,SAAS,MAAM,SAAS;;;;;;;;;;;;;kDAEhG,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAClC,8OAAC;gDAAE,WAAU;;oDAA4B;oDAAG,SAAS,gBAAgB;;;;;;;;;;;;;kDAEvE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAClC,8OAAC;gDAAE,WAAU;;oDAA4B;oDAAG,SAAS,gBAAgB,SAAS,eAC1E,GAAG,QAAQ,YAAY,CAAC,CAAC,EAAE,QAAQ,YAAY,EAAE,GACjD;;;;;;;;;;;;;;;;;;;0CAKR,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAI,WAAU;kDACZ,CAAC;4CACA,MAAM,SAAS,SAAS,UAAU,iBAAiB;4CAEnD,IAAI,MAAM,OAAO,CAAC,WAAW,OAAO,MAAM,GAAG,GAAG;gDAC9C,OAAO,OAAO,GAAG,CAAC,CAAC,OAAO,sBACxB,8OAAC;wDAEC,WAAU;kEAET;uDAHI;;;;;4CAMX,OAAO,IAAI,OAAO,WAAW,YAAY,OAAO,IAAI,IAAI;gDACtD,OAAO,OAAO,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,OAAO,sBACnC,8OAAC;wDAEC,WAAU;kEAET,MAAM,IAAI;uDAHN;;;;;4CAMX,OAAO;gDACL,qBAAO,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;4CACtC;wCACF,CAAC;;;;;;;;;;;;;;;;;;kCAOP,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACf,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACvD,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAsC;;;;;;kEACpD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAA6B;;;;;;0EAC7C,8OAAC;gEAAK,WAAU;0EAA6B;;;;;;0EAC7C,8OAAC;gEAAK,WAAU;0EAAsB,oBAAoB;;;;;;;;;;;;;;;;;;0DAG9D,8OAAC;gDAAI,WAAU;0DACZ,sBAAsB,SAAS,cAAc,SAAS;;;;;;;;;;;;kDAI7D,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;kEACC,cAAA,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAG,WAAU;8EAAuE;;;;;;gEACpF;oEAAC;oEAAG;oEAAG;oEAAG;oEAAG;oEAAG;oEAAG;oEAAG;iEAAE,CAAC,GAAG,CAAC,CAAA,oBAC5B,8OAAC;wEAAa,WAAU;kFAAwE;uEAAvF;;;;;;;;;;;;;;;;kEAIf,8OAAC;kEACC,cAAA,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAAqE;;;;;;gEAClF;oEAAC;oEAAG;oEAAG;oEAAG;oEAAG;oEAAG;oEAAG;oEAAG;iEAAE,CAAC,GAAG,CAAC,CAAA,oBAC5B,8OAAC;wEAAa,WAAU;kFACrB,gBAAgB;uEADV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDASrB,8OAAC;wCAAG,WAAU;;;;;;kDAGd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAsC;;;;;;0EACpD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAA6B,SAAS,gBAAgB;;;;;;kFACtE,8OAAC;wEAAK,WAAU;kFAA6B;;;;;;kFAC7C,8OAAC;wEAAK,WAAU;kFAAsB,SAAS,sBAAsB;;;;;;;;;;;;;;;;;;kEAGzE,8OAAC;wDAAI,WAAU;kEACZ,SAAS,0BACN,GAAG,SAAS,QAAQ,uBAAuB,IAAI,EAAE,GAAG,EAAE,QAAQ,uBAAuB,EAAE,GACvF;;;;;;;;;;;;0DAGR,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAE,WAAU;sFAAkC;;;;;;sFAC/C,8OAAC;4EAAE,WAAU;sFAA6B,SAAS,kBAAkB;;;;;;;;;;;;8EAEvE,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAE,WAAU;sFAAkC;;;;;;sFAC/C,8OAAC;4EAAE,WAAU;sFAA6B,SAAS,iBAAiB;;;;;;;;;;;;;;;;;;sEAGxE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAE,WAAU;sFAAkC;;;;;;sFAC/C,8OAAC;4EAAE,WAAU;sFAA6B,SAAS,QAAQ;;;;;;;;;;;;8EAE7D,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAE,WAAU;sFAAkC;;;;;;sFAC/C,8OAAC;4EAAE,WAAU;sFAA6B,SAAS,0BAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAWvF,8OAAC;wCAAG,WAAU;;;;;;kDAGd,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAsC;;;;;;0EACpD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAA6B,SAAS,cAAc;;;;;;kFACpE,8OAAC;wEAAK,WAAU;kFAA6B;;;;;;kFAC7C,8OAAC;wEAAK,WAAU;kFAAsB,SAAS,oBAAoB;;;;;;;;;;;;;;;;;;kEAGvE,8OAAC;wDAAI,WAAU;kEACZ,SAAS,wBACN,GAAG,SAAS,QAAQ,qBAAqB,IAAI,EAAE,GAAG,EAAE,QAAQ,qBAAqB,EAAE,GACnF;;;;;;;;;;;;0DAIR,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAE,WAAU;sFAAkC;;;;;;sFAC/C,8OAAC;4EAAE,WAAU;sFAA6B,SAAS,gBAAgB;;;;;;;;;;;;8EAErE,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAE,WAAU;sFAAkC;;;;;;sFAC/C,8OAAC;4EAAE,WAAU;sFAA6B,SAAS,eAAe;;;;;;;;;;;;;;;;;;sEAGtE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAE,WAAU;sFAAkC;;;;;;sFAC/C,8OAAC;4EAAE,WAAU;sFAA6B,SAAS,QAAQ;;;;;;;;;;;;8EAE7D,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAE,WAAU;sFAAkC;;;;;;sFAC/C,8OAAC;4EAAE,WAAU;sFAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASrD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;oCAExD,aAAa,OAAO,iBACnB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,8IAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;6DAGlC;;0DAEE,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,8OAAC;4DAAE,WAAU;sEAAuC,aAAa,aAAa;;;;;;;;;;;;;;;;;0DAKlF,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,8OAAC;4DAAE,WAAU;sEAAuC,aAAa,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASzF,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDAEzD,8OAAC;wCACC,WAAU;wCACV,SAAS,IAAM,qBAAqB;;0DAEpC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,OAAM;wDAA6B,WAAU;wDAAU,MAAK;wDAAO,SAAQ;wDAAY,QAAO;kEACjG,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;;;;;;0DAI3E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA4B;;;;;;kEAC1C,8OAAC;wDAAE,WAAU;kEACV,cAAc,IACX,GAAG,YAAY,OAAO,EAAE,cAAc,IAAI,MAAM,GAAG,SAAS,CAAC,GAC7D,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,IAAI,KAAK,kBAAkB,kBAAkB,IAAI,GAAG,EAAE,IAC7F;;;;;;;;;;;;0DAIR,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAA8B;;;;;;;;;;;;;;;;;kDAIlD,8OAAC;wCACC,WAAU;wCACV,SAAS,IAAM,wBAAwB;;0DAEvC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,OAAM;wDAA6B,WAAU;wDAAU,MAAK;wDAAO,SAAQ;wDAAY,QAAO;kEACjG,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;;;;;;0DAI3E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA4B;;;;;;kEAC1C,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DACb;;;;;;;;;;;;;;;;;;;;;;;0CAOT,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAG,WAAU;sDAAsC;;;;;;;;;;;kDAGtD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAqB;;;;;;kEAClC,8OAAC;wDAAE,WAAU;;4DAA4B;4DAAG,SAAS,QAAQ;;;;;;;;;;;;;0DAE/D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAqB;;;;;;kEAClC,8OAAC;wDAAE,WAAU;;4DAA4B;4DAAG,SAAS,YAAY;;;;;;;;;;;;;0DAEnE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAqB;;;;;;kEAClC,8OAAC;wDAAE,WAAU;;4DAA4B;4DAAG,SAAS,SAAS;;;;;;;;;;;;;0DAEhE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAqB;;;;;;kEAClC,8OAAC;wDAAE,WAAU;;4DAA4B;4DAAG,SAAS,WAAW;;;;;;;;;;;;;0DAElE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAqB;;;;;;kEAClC,8OAAC;wDAAE,WAAU;;4DAA4B;4DAAG,SAAS,WAAW;;;;;;;;;;;;;0DAElE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAqB;;;;;;kEAClC,8OAAC;wDAAE,WAAU;;4DAA4B;4DAAG,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ1E,8OAAC,qIAAA,CAAA,UAAW;gBACV,QAAQ;gBACR,SAAS,IAAM,qBAAqB;gBACpC,QAAQ,SAAS,cAAc,SAAS;gBACxC,UAAU;gBACV,UAAU;;;;;;0BAIZ,8OAAC,wIAAA,CAAA,UAAc;gBACb,QAAQ;gBACR,SAAS,IAAM,wBAAwB;gBACvC,WAAW;oBACT,OAAO,SAAS,yBAAyB,SAAS;oBAClD,SAAS,SAAS,2BAA2B,SAAS;oBACtD,oBAAoB;gBACtB;gBACA,qBAAqB,sHAAA,CAAA,cAAW,CAAC,iBAAiB;gBAClD,mBAAmB,sHAAA,CAAA,cAAW,CAAC,uBAAuB;gBACtD,qBAAqB,sHAAA,CAAA,cAAW,CAAC,iBAAiB;gBAClD,mBAAmB,sHAAA,CAAA,cAAW,CAAC,eAAe;;;;;;;;;;;;AAItD", "debugId": null}}]}