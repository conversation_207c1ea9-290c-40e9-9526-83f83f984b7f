# Generated by Django 5.1.5 on 2025-07-06 16:51

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0013_merge_20250702_0500'),
    ]

    operations = [
        migrations.AddField(
            model_name='studentprofile',
            name='allowed_companies',
            field=models.JSONField(blank=True, default=list, help_text='List of allowed company IDs'),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='allowed_job_tiers',
            field=models.JSONField(blank=True, default=list, help_text="List of allowed job tiers (e.g., ['tier1', 'tier2'])"),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='allowed_job_types',
            field=models.JSONField(blank=True, default=list, help_text="List of allowed job types (e.g., ['fulltime', 'internship'])"),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='freeze_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='freeze_reason',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='freeze_status',
            field=models.CharField(choices=[('none', 'None'), ('complete', 'Complete Freeze'), ('partial', 'Partial Freeze')], default='none', max_length=20),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='frozen_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='frozen_students', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='min_salary_requirement',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Minimum salary requirement in lakhs', max_digits=10, null=True),
        ),
    ]
